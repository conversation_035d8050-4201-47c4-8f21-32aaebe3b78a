<?php
/**
 * The template for displaying all single posts
 *
 * @package MR9_Theme
 * @version 1.0.0
 */

get_header(); ?>

<div class="site-container">
    <main class="site-main" id="main">
        <div class="content-area">
            <?php while (have_posts()) : the_post(); ?>
                
                <article id="post-<?php the_ID(); ?>" <?php post_class('post'); ?>>
                    <header class="post-header">
                        <?php
                        // Breadcrumbs
                        if (function_exists('mr9_breadcrumbs')) {
                            mr9_breadcrumbs();
                        }
                        ?>
                        
                        <h1 class="post-title"><?php the_title(); ?></h1>
                        
                        <div class="post-meta">
                            <span class="post-date">
                                <time datetime="<?php echo get_the_date('c'); ?>">
                                    <?php echo get_the_date(); ?>
                                </time>
                            </span>
                            
                            <span class="post-author">
                                <?php esc_html_e('By', 'mr9-theme'); ?> 
                                <a href="<?php echo esc_url(get_author_posts_url(get_the_author_meta('ID'))); ?>">
                                    <?php the_author(); ?>
                                </a>
                            </span>
                            
                            <?php if (has_category()) : ?>
                                <span class="post-categories">
                                    <?php esc_html_e('in', 'mr9-theme'); ?> 
                                    <?php the_category(', '); ?>
                                </span>
                            <?php endif; ?>
                            
                            <span class="post-reading-time">
                                <?php echo mr9_reading_time(); ?> <?php esc_html_e('min read', 'mr9-theme'); ?>
                            </span>
                        </div>
                        
                        <?php if (has_post_thumbnail()) : ?>
                            <div class="post-thumbnail">
                                <?php the_post_thumbnail('large', array('loading' => 'eager')); ?>
                            </div>
                        <?php endif; ?>
                    </header>

                    <?php 
                    // Above content ad
                    get_template_part('ads/ad', 'above-content'); 
                    ?>

                    <div class="post-content">
                        <?php
                        // Split content into paragraphs for ad insertion
                        $content = get_the_content();
                        $content = apply_filters('the_content', $content);
                        $paragraphs = explode('</p>', $content);
                        $paragraph_count = count($paragraphs);
                        
                        foreach ($paragraphs as $index => $paragraph) {
                            if (trim($paragraph) != '') {
                                echo $paragraph . '</p>';
                                
                                // Insert ads between paragraphs
                                if ($index == 2 && $paragraph_count > 5) {
                                    get_template_part('ads/ad', 'in-article-1');
                                }
                                
                                if ($index == floor($paragraph_count / 2) && $paragraph_count > 8) {
                                    get_template_part('ads/ad', 'in-article-2');
                                }
                                
                                if ($index == $paragraph_count - 3 && $paragraph_count > 10) {
                                    get_template_part('ads/ad', 'in-article-3');
                                }
                            }
                        }
                        ?>
                    </div>

                    <?php 
                    // Below content ad
                    get_template_part('ads/ad', 'below-content'); 
                    ?>

                    <footer class="post-footer">
                        <?php if (has_tag()) : ?>
                            <div class="post-tags">
                                <h4><?php esc_html_e('Tags:', 'mr9-theme'); ?></h4>
                                <?php the_tags('<span class="tag">', '</span><span class="tag">', '</span>'); ?>
                            </div>
                        <?php endif; ?>
                        
                        <div class="post-share">
                            <h4><?php esc_html_e('Share this post:', 'mr9-theme'); ?></h4>
                            <?php get_template_part('components/social', 'share'); ?>
                        </div>
                    </footer>
                </article>

                <?php 
                // Related posts ad
                get_template_part('ads/ad', 'related-posts'); 
                ?>

                <?php
                // Related posts
                get_template_part('components/related', 'posts');
                ?>

                <?php
                // Comments
                if (comments_open() || get_comments_number()) {
                    comments_template();
                }
                ?>

            <?php endwhile; ?>
        </div>

        <?php get_sidebar(); ?>
    </main>
</div>

<?php
// Sticky mobile ad
get_template_part('ads/ad', 'sticky-mobile');

get_footer();
?>
