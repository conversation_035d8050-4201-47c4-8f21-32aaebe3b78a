<?php
/**
 * Template part for displaying posts
 *
 * @package MR9_Theme
 * @version 1.0.0
 */
?>

<article id="post-<?php the_ID(); ?>" <?php post_class('post post-excerpt'); ?>>
    <header class="post-header">
        <?php if (has_post_thumbnail()) : ?>
            <div class="post-thumbnail">
                <a href="<?php the_permalink(); ?>" aria-hidden="true" tabindex="-1">
                    <?php the_post_thumbnail('ad-featured', array('loading' => 'lazy')); ?>
                </a>
            </div>
        <?php endif; ?>
        
        <div class="post-header-content">
            <?php if (has_category()) : ?>
                <div class="post-categories">
                    <?php the_category(' '); ?>
                </div>
            <?php endif; ?>
            
            <?php
            if (is_singular()) :
                the_title('<h1 class="post-title">', '</h1>');
            else :
                the_title('<h2 class="post-title"><a href="' . esc_url(get_permalink()) . '" rel="bookmark">', '</a></h2>');
            endif;
            ?>
            
            <div class="post-meta">
                <span class="post-date">
                    <time datetime="<?php echo get_the_date('c'); ?>">
                        <?php echo get_the_date(); ?>
                    </time>
                </span>
                
                <span class="post-author">
                    <?php esc_html_e('By', 'mr9-theme'); ?> 
                    <a href="<?php echo esc_url(get_author_posts_url(get_the_author_meta('ID'))); ?>">
                        <?php the_author(); ?>
                    </a>
                </span>
                
                <span class="post-reading-time">
                    <?php echo mr9_reading_time(); ?> <?php esc_html_e('min read', 'mr9-theme'); ?>
                </span>
                
                <?php if (comments_open() || get_comments_number()) : ?>
                    <span class="post-comments">
                        <a href="<?php comments_link(); ?>">
                            <?php comments_number('0 Comments', '1 Comment', '% Comments'); ?>
                        </a>
                    </span>
                <?php endif; ?>
            </div>
        </div>
    </header>

    <div class="post-content">
        <?php
        if (is_singular()) {
            the_content(sprintf(
                wp_kses(
                    __('Continue reading<span class="screen-reader-text"> "%s"</span>', 'mr9-theme'),
                    array(
                        'span' => array(
                            'class' => array(),
                        ),
                    )
                ),
                wp_kses_post(get_the_title())
            ));

            wp_link_pages(array(
                'before' => '<div class="page-links">' . esc_html__('Pages:', 'mr9-theme'),
                'after' => '</div>',
            ));
        } else {
            the_excerpt();
            ?>
            <div class="read-more">
                <a href="<?php the_permalink(); ?>" class="read-more-link">
                    <?php esc_html_e('Read More', 'mr9-theme'); ?>
                    <span class="screen-reader-text"><?php echo get_the_title(); ?></span>
                </a>
            </div>
            <?php
        }
        ?>
    </div>

    <?php if (has_tag() && is_singular()) : ?>
        <footer class="post-footer">
            <div class="post-tags">
                <?php the_tags('<span class="tag">', '</span><span class="tag">', '</span>'); ?>
            </div>
        </footer>
    <?php endif; ?>
</article>
