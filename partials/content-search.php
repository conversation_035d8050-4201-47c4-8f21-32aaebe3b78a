<?php
/**
 * Template part for displaying results in search pages
 *
 * @package MR9_Theme
 * @version 1.0.0
 */
?>

<article id="post-<?php the_ID(); ?>" <?php post_class('post post-search-result'); ?>>
    <header class="post-header">
        <?php if (has_post_thumbnail()) : ?>
            <div class="post-thumbnail">
                <a href="<?php the_permalink(); ?>" aria-hidden="true" tabindex="-1">
                    <?php the_post_thumbnail('ad-thumbnail', array('loading' => 'lazy')); ?>
                </a>
            </div>
        <?php endif; ?>
        
        <div class="post-header-content">
            <?php the_title('<h2 class="post-title"><a href="' . esc_url(get_permalink()) . '" rel="bookmark">', '</a></h2>'); ?>
            
            <div class="post-meta">
                <span class="post-date">
                    <time datetime="<?php echo get_the_date('c'); ?>">
                        <?php echo get_the_date(); ?>
                    </time>
                </span>
                
                <span class="post-author">
                    <?php esc_html_e('By', 'mr9-theme'); ?> 
                    <a href="<?php echo esc_url(get_author_posts_url(get_the_author_meta('ID'))); ?>">
                        <?php the_author(); ?>
                    </a>
                </span>
                
                <?php if (has_category()) : ?>
                    <span class="post-categories">
                        <?php esc_html_e('in', 'mr9-theme'); ?> 
                        <?php the_category(', '); ?>
                    </span>
                <?php endif; ?>
            </div>
        </div>
    </header>

    <div class="post-content">
        <?php the_excerpt(); ?>
        
        <div class="read-more">
            <a href="<?php the_permalink(); ?>" class="read-more-link">
                <?php esc_html_e('Read More', 'mr9-theme'); ?>
                <span class="screen-reader-text"><?php echo get_the_title(); ?></span>
            </a>
        </div>
    </div>
</article>
