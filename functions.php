<?php
/**
 * MR9 SEO AdSense Theme Functions
 * 
 * @package MR9_Theme
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Theme constants
define('MR9_THEME_VERSION', '1.0.0');
define('MR9_THEME_DIR', get_template_directory());
define('MR9_THEME_URI', get_template_directory_uri());

/**
 * Theme Setup
 */
function mr9_theme_setup() {
    // Add theme support
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
        'style',
        'script'
    ));
    add_theme_support('custom-logo');
    add_theme_support('responsive-embeds');
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'mr9-theme'),
        'footer' => __('Footer Menu', 'mr9-theme'),
    ));
    
    // Add image sizes for ads optimization
    add_image_size('ad-featured', 728, 400, true);
    add_image_size('ad-thumbnail', 300, 200, true);
    add_image_size('mobile-featured', 350, 200, true);
}
add_action('after_setup_theme', 'mr9_theme_setup');

/**
 * Enqueue Scripts and Styles
 */
function mr9_theme_scripts() {
    // Main stylesheet
    wp_enqueue_style('mr9-style', get_stylesheet_uri(), array(), MR9_THEME_VERSION);
    
    // Custom JavaScript
    wp_enqueue_script('mr9-main', MR9_THEME_URI . '/assets/js/main.js', array('jquery'), MR9_THEME_VERSION, true);
    
    // Ad optimization scripts
    wp_enqueue_script('mr9-ads', MR9_THEME_URI . '/assets/js/ads.js', array('jquery'), MR9_THEME_VERSION, true);
    
    // Localize script for AJAX
    wp_localize_script('mr9-main', 'mr9_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('mr9_nonce'),
        'is_mobile' => wp_is_mobile()
    ));
    
    // Comment reply script
    if (is_singular() && comments_open() && get_option('thread_comments')) {
        wp_enqueue_script('comment-reply');
    }
}
add_action('wp_enqueue_scripts', 'mr9_theme_scripts');

/**
 * Register Widget Areas
 */
function mr9_theme_widgets_init() {
    register_sidebar(array(
        'name' => __('Primary Sidebar', 'mr9-theme'),
        'id' => 'sidebar-1',
        'description' => __('Add widgets here to appear in your sidebar.', 'mr9-theme'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget' => '</section>',
        'before_title' => '<h3 class="widget-title">',
        'after_title' => '</h3>',
    ));
    
    register_sidebar(array(
        'name' => __('Footer Widgets', 'mr9-theme'),
        'id' => 'footer-widgets',
        'description' => __('Add widgets here to appear in your footer.', 'mr9-theme'),
        'before_widget' => '<div id="%1$s" class="footer-widget %2$s">',
        'after_widget' => '</div>',
        'before_title' => '<h4 class="footer-widget-title">',
        'after_title' => '</h4>',
    ));
}
add_action('widgets_init', 'mr9_theme_widgets_init');

/**
 * Include required files
 */
require_once MR9_THEME_DIR . '/inc/theme-options.php';
require_once MR9_THEME_DIR . '/inc/ad-functions.php';
require_once MR9_THEME_DIR . '/inc/performance-optimization.php';
require_once MR9_THEME_DIR . '/inc/seo-functions.php';
require_once MR9_THEME_DIR . '/inc/customizer.php';

/**
 * Custom excerpt length
 */
function mr9_excerpt_length($length) {
    return 25;
}
add_filter('excerpt_length', 'mr9_excerpt_length');

/**
 * Custom excerpt more
 */
function mr9_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'mr9_excerpt_more');

/**
 * Add async/defer attributes to scripts
 */
function mr9_add_async_defer_attributes($tag, $handle, $src) {
    $async_scripts = array('mr9-ads');
    $defer_scripts = array('mr9-main');
    
    if (in_array($handle, $async_scripts)) {
        return str_replace('<script ', '<script async ', $tag);
    }
    
    if (in_array($handle, $defer_scripts)) {
        return str_replace('<script ', '<script defer ', $tag);
    }
    
    return $tag;
}
add_filter('script_loader_tag', 'mr9_add_async_defer_attributes', 10, 3);

/**
 * Disable Gutenberg for posts (Classic Editor only)
 */
function mr9_disable_gutenberg($current_status, $post_type) {
    if ($post_type === 'post' || $post_type === 'page') {
        return false;
    }
    return $current_status;
}
add_filter('use_block_editor_for_post_type', 'mr9_disable_gutenberg', 10, 2);

/**
 * Remove unnecessary WordPress features for performance
 */
function mr9_remove_wp_features() {
    // Remove emoji scripts
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');
    
    // Remove WordPress version
    remove_action('wp_head', 'wp_generator');
    
    // Remove RSD link
    remove_action('wp_head', 'rsd_link');
    
    // Remove wlwmanifest link
    remove_action('wp_head', 'wlwmanifest_link');
    
    // Remove shortlink
    remove_action('wp_head', 'wp_shortlink_wp_head');
    
    // Remove feed links
    remove_action('wp_head', 'feed_links_extra', 3);
    remove_action('wp_head', 'feed_links', 2);
}
add_action('init', 'mr9_remove_wp_features');

/**
 * Add preload for critical resources
 */
function mr9_add_preload_links() {
    echo '<link rel="preload" href="' . MR9_THEME_URI . '/assets/css/critical.css" as="style">' . "\n";
    echo '<link rel="preconnect" href="https://fonts.googleapis.com">' . "\n";
    echo '<link rel="preconnect" href="https://pagead2.googlesyndication.com">' . "\n";
    echo '<link rel="dns-prefetch" href="//www.googletagservices.com">' . "\n";
}
add_action('wp_head', 'mr9_add_preload_links', 1);

/**
 * Add structured data for better SEO
 */
function mr9_add_structured_data() {
    if (is_single()) {
        global $post;
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'Article',
            'headline' => get_the_title(),
            'author' => array(
                '@type' => 'Person',
                'name' => get_the_author()
            ),
            'datePublished' => get_the_date('c'),
            'dateModified' => get_the_modified_date('c'),
            'publisher' => array(
                '@type' => 'Organization',
                'name' => get_bloginfo('name'),
                'logo' => array(
                    '@type' => 'ImageObject',
                    'url' => get_site_icon_url()
                )
            )
        );
        
        if (has_post_thumbnail()) {
            $schema['image'] = wp_get_attachment_image_url(get_post_thumbnail_id(), 'full');
        }
        
        echo '<script type="application/ld+json">' . json_encode($schema) . '</script>' . "\n";
    }
}
add_action('wp_head', 'mr9_add_structured_data');

/**
 * Security enhancements
 */
function mr9_security_headers() {
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: SAMEORIGIN');
    header('X-XSS-Protection: 1; mode=block');
}
add_action('send_headers', 'mr9_security_headers');

/**
 * Optimize database queries
 */
function mr9_optimize_queries() {
    // Remove unnecessary queries
    remove_action('wp_head', 'adjacent_posts_rel_link_wp_head', 10);
    
    // Limit post revisions
    if (!defined('WP_POST_REVISIONS')) {
        define('WP_POST_REVISIONS', 3);
    }
}
add_action('init', 'mr9_optimize_queries');
