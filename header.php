<?php
/**
 * The header for our theme
 *
 * @package MR9_Theme
 * @version 1.0.0
 */
?>
<!doctype html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">
    
    <?php 
    // Critical CSS inline for performance
    $critical_css = MR9_THEME_DIR . '/assets/css/critical.css';
    if (file_exists($critical_css)) {
        echo '<style>' . file_get_contents($critical_css) . '</style>';
    }
    ?>
    
    <?php wp_head(); ?>
    
    <?php 
    // AdSense Auto Ads (if enabled in theme options)
    if (get_theme_mod('mr9_adsense_auto_ads', false)) {
        $adsense_client = get_theme_mod('mr9_adsense_client_id', '');
        if (!empty($adsense_client)) {
            echo '<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=' . esc_attr($adsense_client) . '" crossorigin="anonymous"></script>';
        }
    }
    ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<div id="page" class="site">
    <a class="skip-link screen-reader-text" href="#main"><?php esc_html_e('Skip to content', 'mr9-theme'); ?></a>

    <header id="masthead" class="site-header">
        <div class="header-container">
            <div class="site-branding">
                <?php
                if (has_custom_logo()) {
                    the_custom_logo();
                } else {
                    ?>
                    <h1 class="site-logo">
                        <a href="<?php echo esc_url(home_url('/')); ?>" rel="home">
                            <?php bloginfo('name'); ?>
                        </a>
                    </h1>
                    <?php
                    $description = get_bloginfo('description', 'display');
                    if ($description || is_customize_preview()) {
                        ?>
                        <p class="site-description"><?php echo $description; ?></p>
                        <?php
                    }
                }
                ?>
            </div>

            <nav id="site-navigation" class="main-navigation">
                <button class="menu-toggle" aria-controls="primary-menu" aria-expanded="false">
                    <span class="menu-toggle-text"><?php esc_html_e('Menu', 'mr9-theme'); ?></span>
                    <span class="menu-icon">
                        <span></span>
                        <span></span>
                        <span></span>
                    </span>
                </button>
                
                <?php
                wp_nav_menu(array(
                    'theme_location' => 'primary',
                    'menu_id' => 'primary-menu',
                    'container' => false,
                    'fallback_cb' => 'mr9_fallback_menu',
                ));
                ?>
            </nav>
        </div>
    </header>

    <?php
    // Header ad placement (leaderboard)
    if (!is_404() && get_theme_mod('mr9_enable_header_ads', true)) {
        get_template_part('ads/ad', 'header-leaderboard');
    }
    ?>
