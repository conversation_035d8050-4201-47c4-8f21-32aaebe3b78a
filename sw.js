/**
 * Service Worker for MR9 Theme
 * Provides caching for better performance
 */

const CACHE_NAME = 'mr9-theme-v1';
const urlsToCache = [
    '/',
    '/wp-content/themes/mr9-theme-seo/style.css',
    '/wp-content/themes/mr9-theme-seo/assets/js/main.js',
    '/wp-content/themes/mr9-theme-seo/assets/js/ads.js',
    '/wp-content/themes/mr9-theme-seo/assets/css/critical.css'
];

// Install event
self.addEventListener('install', function(event) {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(function(cache) {
                return cache.addAll(urlsToCache);
            })
    );
});

// Fetch event
self.addEventListener('fetch', function(event) {
    event.respondWith(
        caches.match(event.request)
            .then(function(response) {
                // Return cached version or fetch from network
                return response || fetch(event.request);
            }
        )
    );
});

// Activate event
self.addEventListener('activate', function(event) {
    event.waitUntil(
        caches.keys().then(function(cacheNames) {
            return Promise.all(
                cacheNames.map(function(cacheName) {
                    if (cacheName !== CACHE_NAME) {
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
});
