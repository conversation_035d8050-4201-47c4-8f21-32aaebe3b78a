<?php
/**
 * <PERSON><PERSON> Banner Ad Template
 *
 * @package MR9_Theme
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check if header ads are enabled
if (!get_theme_mod('mr9_enable_header_ads', true) || !mr9_should_display_ads()) {
    return;
}

$desktop_slot = get_theme_mod('mr9_header_ad_slot_desktop', '');
$mobile_slot = get_theme_mod('mr9_header_ad_slot_mobile', '');

if (empty($desktop_slot) && empty($mobile_slot)) {
    return;
}
?>

<div class="header-ad-container">
    <?php mr9_display_responsive_ad($desktop_slot, $mobile_slot, 'ad-leaderboard'); ?>
</div>
