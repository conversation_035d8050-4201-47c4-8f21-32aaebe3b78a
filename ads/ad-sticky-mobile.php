<?php
/**
 * Sticky Mobile Ad Template
 *
 * @package MR9_Theme
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check if mobile ads are enabled and user is on mobile
if (!get_theme_mod('mr9_enable_mobile_ads', true) || !mr9_should_display_ads() || !wp_is_mobile()) {
    return;
}

$slot_id = get_theme_mod('mr9_mobile_sticky_ad_slot', '');

if (empty($slot_id)) {
    return;
}
?>

<?php mr9_display_sticky_mobile_ad($slot_id); ?>
