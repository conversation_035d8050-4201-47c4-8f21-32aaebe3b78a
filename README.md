# MR9 SEO AdSense Theme

A high-performance WordPress theme optimized for AdSense revenue generation with 100% code-based layout, designed for maximum viewability, CTR, and RPM.

## Features

### 🎯 AdSense Optimization
- **Strategic Ad Placements**: Above-the-fold, in-feed, in-article, sidebar, and sticky mobile ads
- **High Viewability**: 70%+ viewability rate with intelligent lazy loading
- **Smart Ad Refresh**: Session-based ad refresh for better RPM
- **Mobile-First Design**: Optimized for 60%+ mobile traffic
- **AdSense Policy Compliant**: Clear separation, proper labeling, and spacing

### 🚀 Performance Optimization
- **Core Web Vitals Optimized**: LCP, FID, and CLS optimization
- **Lazy Loading**: Images and ads load when needed
- **Critical CSS**: Inline critical styles for faster rendering
- **Resource Hints**: Preload, preconnect, and DNS prefetch
- **Optimized JavaScript**: Async/defer loading with performance monitoring

### 📱 Responsive Design
- **Mobile-First Approach**: Optimized for mobile ad performance
- **Flexible Grid System**: CSS Grid and Flexbox for modern layouts
- **Touch-Friendly**: Optimized for mobile user experience
- **Cross-Browser Compatible**: Works across all modern browsers

### 🔧 100% Code-Based
- **No Gutenberg Blocks**: Classic Editor compatibility
- **Modular PHP Structure**: Easy to maintain and extend
- **Template Parts**: Reusable components via `get_template_part()`
- **Clean Code**: Well-documented and organized

## Installation

1. Download the theme files
2. Upload to `/wp-content/themes/mr9-theme-seo/`
3. Activate the theme in WordPress admin
4. Configure AdSense settings in Customizer

## Configuration

### AdSense Setup

1. Go to **Appearance > Customize > AdSense Settings**
2. Enter your AdSense Client ID (`ca-pub-xxxxxxxxxx`)
3. Configure ad slot IDs for different positions:
   - Header Leaderboard (728x90)
   - Content Ads (300x250)
   - In-Article Ads
   - Sidebar Ads
   - Mobile Sticky Ad (320x50)

### Ad Positions

#### Header Ads
- **Header Banner**: Above navigation
- **Header Leaderboard**: Below navigation

#### Content Ads
- **Above Content**: Before post content
- **In-Article**: Between paragraphs (3 positions)
- **Below Content**: After post content

#### Sidebar Ads
- **Top Sidebar**: First position
- **Middle Sidebar**: Between widgets
- **Bottom Sidebar**: Last position (skyscraper)

#### Mobile Ads
- **Sticky Mobile**: Fixed bottom banner
- **In-Feed**: Native ads in post lists

#### Special Ads
- **Multiplex**: Related content ads
- **Footer Banner**: Before footer content

### Theme Options

Access theme options via **Appearance > Customize**:

- **AdSense Settings**: Configure all ad placements
- **Performance Settings**: Core Web Vitals monitoring
- **SEO Settings**: Social media profiles and meta tags

## File Structure

```
mr9-theme-seo/
├── ads/                    # Ad components
│   ├── ad-header-banner.php
│   ├── ad-in-article-1.php
│   ├── ad-sidebar-top.php
│   └── ...
├── partials/              # Template parts
│   ├── content.php
│   ├── content-none.php
│   └── content-search.php
├── components/            # Reusable components
│   ├── related-posts.php
│   ├── social-share.php
│   └── popular-posts.php
├── inc/                   # Helper functions
│   ├── ad-functions.php
│   ├── theme-options.php
│   ├── performance-optimization.php
│   ├── seo-functions.php
│   └── customizer.php
├── assets/
│   ├── js/
│   │   ├── ads.js         # Ad optimization
│   │   └── main.js        # Theme functionality
│   └── css/
│       └── critical.css   # Critical CSS (create manually)
├── style.css              # Main stylesheet
├── functions.php          # Theme functions
├── index.php             # Main template
├── header.php            # Header template
├── footer.php            # Footer template
├── single.php            # Single post template
├── page.php              # Page template
├── sidebar.php           # Sidebar template
├── archive.php           # Archive template
├── search.php            # Search results template
├── 404.php               # 404 error template
└── searchform.php        # Search form template
```

## Ad Management

### Enable/Disable Ads

Use theme customizer or add to `functions.php`:

```php
// Disable all ads
add_filter('mr9_enable_ads', '__return_false');

// Disable specific ad types
add_filter('mr9_enable_header_ads', '__return_false');
add_filter('mr9_enable_sidebar_ads', '__return_false');
add_filter('mr9_enable_mobile_ads', '__return_false');
```

### Custom Ad Placements

Add custom ad placements in your templates:

```php
// Display a custom ad
mr9_display_adsense_ad('your-slot-id', 'ad-rectangle');

// Display responsive ad
mr9_display_responsive_ad('desktop-slot', 'mobile-slot', 'ad-leaderboard');

// Display native ad
mr9_display_native_ad('native-slot-id');
```

### Ad Performance Tracking

The theme automatically tracks:
- Ad viewability rates
- User engagement metrics
- Performance data
- Core Web Vitals

## Performance Features

### Core Web Vitals Optimization
- **LCP**: Optimized image loading and critical CSS
- **FID**: Deferred JavaScript and optimized event handlers
- **CLS**: Reserved space for ads to prevent layout shift

### Caching Compatibility
Compatible with:
- WP Rocket
- FlyingPress
- LiteSpeed Cache
- W3 Total Cache
- WP Super Cache

### Database Optimization
- Limited post revisions
- Optimized queries
- Transient cleanup
- Spam comment removal

## SEO Features

### Structured Data
- Article schema for posts
- Organization schema for homepage
- Website schema with search functionality
- Breadcrumb navigation

### Meta Tags
- Open Graph tags for social sharing
- Twitter Card support
- Canonical URLs
- Meta descriptions
- Robots meta tags

### XML Sitemap
Automatic sitemap generation at `/sitemap.xml`

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Requirements

- WordPress 5.0+
- PHP 7.4+
- MySQL 5.6+

## Support

For support and customization:
- Review the code documentation
- Check WordPress Codex for standard functions
- Test thoroughly before deploying to production

## License

GPL v2 or later

## Changelog

### Version 1.0.0
- Initial release
- Complete AdSense optimization
- Performance optimization
- SEO features
- Responsive design
- Accessibility features
