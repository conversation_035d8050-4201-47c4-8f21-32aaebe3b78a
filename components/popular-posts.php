<?php
/**
 * Popular Posts Component
 *
 * @package MR9_Theme
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get popular posts
$popular_posts = mr9_get_popular_posts();

if (empty($popular_posts)) {
    return;
}
?>

<section class="popular-posts widget">
    <h3 class="widget-title"><?php esc_html_e('Popular Posts', 'mr9-theme'); ?></h3>
    
    <div class="popular-posts-list">
        <?php foreach ($popular_posts as $post) : setup_postdata($post); ?>
            <article class="popular-post">
                <?php if (has_post_thumbnail()) : ?>
                    <div class="popular-post-thumbnail">
                        <a href="<?php the_permalink(); ?>">
                            <?php the_post_thumbnail('ad-thumbnail', array('loading' => 'lazy')); ?>
                        </a>
                    </div>
                <?php endif; ?>
                
                <div class="popular-post-content">
                    <h4 class="popular-post-title">
                        <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                    </h4>
                    
                    <div class="popular-post-meta">
                        <span class="popular-post-date"><?php echo get_the_date(); ?></span>
                        <span class="popular-post-views">
                            <?php echo mr9_get_post_views(get_the_ID()); ?> <?php esc_html_e('views', 'mr9-theme'); ?>
                        </span>
                    </div>
                </div>
            </article>
        <?php endforeach; ?>
        <?php wp_reset_postdata(); ?>
    </div>
</section>
