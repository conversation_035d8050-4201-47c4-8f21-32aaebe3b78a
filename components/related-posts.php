<?php
/**
 * Related Posts Component
 *
 * @package MR9_Theme
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Only show on single posts
if (!is_single()) {
    return;
}

// Get related posts
$related_posts = mr9_get_related_posts();

if (empty($related_posts)) {
    return;
}
?>

<section class="related-posts">
    <h3 class="related-posts-title"><?php esc_html_e('Related Articles', 'mr9-theme'); ?></h3>
    
    <div class="related-posts-grid">
        <?php foreach ($related_posts as $post) : setup_postdata($post); ?>
            <article class="related-post">
                <?php if (has_post_thumbnail()) : ?>
                    <div class="related-post-thumbnail">
                        <a href="<?php the_permalink(); ?>">
                            <?php the_post_thumbnail('ad-thumbnail', array('loading' => 'lazy')); ?>
                        </a>
                    </div>
                <?php endif; ?>
                
                <div class="related-post-content">
                    <h4 class="related-post-title">
                        <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                    </h4>
                    
                    <div class="related-post-meta">
                        <span class="related-post-date"><?php echo get_the_date(); ?></span>
                        <span class="related-post-reading-time">
                            <?php echo mr9_reading_time(); ?> <?php esc_html_e('min read', 'mr9-theme'); ?>
                        </span>
                    </div>
                    
                    <div class="related-post-excerpt">
                        <?php echo wp_trim_words(get_the_excerpt(), 15, '...'); ?>
                    </div>
                </div>
            </article>
        <?php endforeach; ?>
        <?php wp_reset_postdata(); ?>
    </div>
</section>
