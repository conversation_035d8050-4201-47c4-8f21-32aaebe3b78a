<?php
/**
 * The main template file
 *
 * @package MR9_Theme
 * @version 1.0.0
 */

get_header(); ?>

<div class="site-container">
    <?php 
    // Above-the-fold ad placement
    get_template_part('ads/ad', 'header-banner'); 
    ?>
    
    <main class="site-main" id="main">
        <div class="content-area">
            <?php if (have_posts()) : ?>
                
                <?php if (is_home() && !is_front_page()) : ?>
                    <header class="page-header">
                        <h1 class="page-title"><?php single_post_title(); ?></h1>
                    </header>
                <?php endif; ?>

                <div class="posts-container">
                    <?php
                    $post_count = 0;
                    while (have_posts()) :
                        the_post();
                        $post_count++;
                        
                        // Display post
                        get_template_part('partials/content', get_post_type());
                        
                        // Insert in-feed ads after specific posts
                        if ($post_count == 2) {
                            get_template_part('ads/ad', 'in-feed-1');
                        }
                        
                        if ($post_count == 5) {
                            get_template_part('ads/ad', 'in-feed-2');
                        }
                        
                        if ($post_count == 8) {
                            get_template_part('ads/ad', 'multiplex');
                        }
                        
                    endwhile;
                    ?>
                </div>

                <?php
                // Pagination
                the_posts_pagination(array(
                    'mid_size' => 2,
                    'prev_text' => __('&laquo; Previous', 'mr9-theme'),
                    'next_text' => __('Next &raquo;', 'mr9-theme'),
                    'class' => 'pagination'
                ));
                ?>

            <?php else : ?>
                
                <?php get_template_part('partials/content', 'none'); ?>
                
            <?php endif; ?>
        </div>

        <?php get_sidebar(); ?>
    </main>
</div>

<?php
// Sticky mobile ad
get_template_part('ads/ad', 'sticky-mobile');

get_footer();
?>
