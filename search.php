<?php
/**
 * The template for displaying search results pages
 *
 * @package MR9_Theme
 * @version 1.0.0
 */

get_header(); ?>

<div class="site-container">
    <main class="site-main" id="main">
        <div class="content-area">
            <header class="page-header">
                <h1 class="page-title">
                    <?php
                    printf(
                        esc_html__('Search Results for: %s', 'mr9-theme'),
                        '<span>' . get_search_query() . '</span>'
                    );
                    ?>
                </h1>
            </header>

            <?php if (have_posts()) : ?>
                
                <div class="posts-container">
                    <?php
                    $post_count = 0;
                    while (have_posts()) :
                        the_post();
                        $post_count++;
                        
                        // Display post
                        get_template_part('partials/content', 'search');
                        
                        // Insert ads in search results
                        if ($post_count == 4) {
                            get_template_part('ads/ad', 'in-feed-1');
                        }
                        
                    endwhile;
                    ?>
                </div>

                <?php
                // Pagination
                the_posts_pagination(array(
                    'mid_size' => 2,
                    'prev_text' => __('&laquo; Previous', 'mr9-theme'),
                    'next_text' => __('Next &raquo;', 'mr9-theme'),
                    'class' => 'pagination'
                ));
                ?>

            <?php else : ?>
                
                <?php get_template_part('partials/content', 'none'); ?>
                
            <?php endif; ?>
        </div>

        <?php get_sidebar(); ?>
    </main>
</div>

<?php get_footer(); ?>
