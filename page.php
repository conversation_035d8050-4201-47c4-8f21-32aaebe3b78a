<?php
/**
 * The template for displaying all pages
 *
 * @package MR9_Theme
 * @version 1.0.0
 */

get_header(); ?>

<div class="site-container">
    <main class="site-main" id="main">
        <div class="content-area">
            <?php while (have_posts()) : the_post(); ?>
                
                <article id="post-<?php the_ID(); ?>" <?php post_class('page'); ?>>
                    <header class="page-header">
                        <?php
                        // Breadcrumbs
                        if (function_exists('mr9_breadcrumbs')) {
                            mr9_breadcrumbs();
                        }
                        ?>
                        
                        <h1 class="page-title"><?php the_title(); ?></h1>
                        
                        <?php if (has_post_thumbnail()) : ?>
                            <div class="page-thumbnail">
                                <?php the_post_thumbnail('large'); ?>
                            </div>
                        <?php endif; ?>
                    </header>

                    <?php 
                    // Above content ad (only if enabled for pages)
                    if (get_theme_mod('mr9_enable_page_ads', false)) {
                        get_template_part('ads/ad', 'above-content'); 
                    }
                    ?>

                    <div class="page-content">
                        <?php
                        the_content();
                        
                        wp_link_pages(array(
                            'before' => '<div class="page-links">' . esc_html__('Pages:', 'mr9-theme'),
                            'after' => '</div>',
                        ));
                        ?>
                    </div>

                    <?php 
                    // Below content ad (only if enabled for pages)
                    if (get_theme_mod('mr9_enable_page_ads', false)) {
                        get_template_part('ads/ad', 'below-content'); 
                    }
                    ?>
                </article>

                <?php
                // Comments (if enabled for pages)
                if (comments_open() || get_comments_number()) {
                    comments_template();
                }
                ?>

            <?php endwhile; ?>
        </div>

        <?php 
        // Show sidebar only if not a full-width page template
        if (!is_page_template('page-templates/full-width.php')) {
            get_sidebar(); 
        }
        ?>
    </main>
</div>

<?php get_footer(); ?>
