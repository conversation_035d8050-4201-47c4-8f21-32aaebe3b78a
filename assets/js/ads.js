/**
 * AdSense Optimization JavaScript
 * 
 * @package MR9_Theme
 * @version 1.0.0
 */

(function($) {
    'use strict';
    
    // Ad optimization object
    const MR9Ads = {
        
        // Configuration
        config: {
            viewabilityThreshold: 0.5,
            refreshInterval: 30000,
            stickyAdDelay: 3000,
            maxRefreshCount: 3
        },
        
        // State tracking
        state: {
            refreshCount: 0,
            userEngaged: false,
            adsLoaded: false,
            viewabilityObserver: null
        },
        
        // Initialize ad optimization
        init: function() {
            this.setupViewabilityTracking();
            this.setupStickyAds();
            this.setupAdRefresh();
            this.setupLazyLoading();
            this.trackUserEngagement();
            this.optimizeAdSizes();
        },
        
        // Setup viewability tracking for better RPM
        setupViewabilityTracking: function() {
            if (!window.IntersectionObserver) return;
            
            this.state.viewabilityObserver = new IntersectionObserver(
                this.handleViewabilityChange.bind(this),
                {
                    threshold: [0.5, 0.75, 1.0],
                    rootMargin: '0px'
                }
            );
            
            // Observe all ad containers
            $('.ad-container').each(function() {
                MR9Ads.state.viewabilityObserver.observe(this);
            });
        },
        
        // Handle viewability changes
        handleViewabilityChange: function(entries) {
            entries.forEach(function(entry) {
                const adContainer = $(entry.target);
                const viewability = entry.intersectionRatio;
                
                if (viewability >= 0.5) {
                    adContainer.addClass('viewable');
                    MR9Ads.trackAdViewability(adContainer, viewability);
                } else {
                    adContainer.removeClass('viewable');
                }
            });
        },
        
        // Track ad viewability for analytics
        trackAdViewability: function(adContainer, viewability) {
            const adId = adContainer.attr('id') || 'unknown';
            
            // Send to Google Analytics if available
            if (typeof gtag !== 'undefined') {
                gtag('event', 'ad_viewability', {
                    'event_category': 'Ads',
                    'event_label': adId,
                    'value': Math.round(viewability * 100)
                });
            }
            
            // Custom tracking
            this.logAdEvent('viewability', {
                adId: adId,
                viewability: viewability,
                timestamp: Date.now()
            });
        },
        
        // Setup sticky mobile ads
        setupStickyAds: function() {
            const stickyAd = $('#sticky-mobile-ad');
            if (stickyAd.length === 0) return;
            
            // Show sticky ad after delay
            setTimeout(function() {
                if (MR9Ads.state.userEngaged) {
                    stickyAd.addClass('active');
                }
            }, this.config.stickyAdDelay);
            
            // Handle close button
            stickyAd.find('.ad-close').on('click', function() {
                stickyAd.removeClass('active');
                localStorage.setItem('mr9_sticky_ad_closed', Date.now());
            });
            
            // Check if user previously closed sticky ad
            const lastClosed = localStorage.getItem('mr9_sticky_ad_closed');
            if (lastClosed && (Date.now() - lastClosed) < 86400000) { // 24 hours
                stickyAd.remove();
            }
        },
        
        // Setup ad refresh for better RPM
        setupAdRefresh: function() {
            if (typeof googletag === 'undefined') return;
            
            // Refresh ads after user engagement
            $(document).on('scroll click', function() {
                if (!MR9Ads.state.userEngaged) {
                    MR9Ads.state.userEngaged = true;
                    MR9Ads.scheduleAdRefresh();
                }
            });
        },
        
        // Schedule ad refresh
        scheduleAdRefresh: function() {
            if (this.state.refreshCount >= this.config.maxRefreshCount) return;
            
            setTimeout(function() {
                if (MR9Ads.shouldRefreshAds()) {
                    MR9Ads.refreshAds();
                }
            }, this.config.refreshInterval);
        },
        
        // Check if ads should be refreshed
        shouldRefreshAds: function() {
            // Only refresh if user is still engaged
            const lastActivity = this.getLastActivity();
            return (Date.now() - lastActivity) < 10000; // 10 seconds
        },
        
        // Refresh ads
        refreshAds: function() {
            if (typeof googletag !== 'undefined') {
                // Refresh only viewable ads
                const viewableSlots = [];
                $('.ad-container.viewable').each(function() {
                    const slot = googletag.getSlots().find(s => 
                        s.getSlotElementId() === this.id
                    );
                    if (slot) viewableSlots.push(slot);
                });
                
                if (viewableSlots.length > 0) {
                    googletag.pubads().refresh(viewableSlots);
                    this.state.refreshCount++;
                    this.logAdEvent('refresh', {
                        count: this.state.refreshCount,
                        slots: viewableSlots.length
                    });
                }
            }
            
            // Schedule next refresh
            if (this.state.refreshCount < this.config.maxRefreshCount) {
                this.scheduleAdRefresh();
            }
        },
        
        // Setup lazy loading for ads
        setupLazyLoading: function() {
            if (!window.IntersectionObserver) {
                // Fallback: load all ads immediately
                this.loadAllAds();
                return;
            }
            
            const lazyObserver = new IntersectionObserver(
                this.handleLazyLoad.bind(this),
                { rootMargin: '100px' }
            );
            
            $('.ad-container:not(.loaded)').each(function() {
                lazyObserver.observe(this);
            });
        },
        
        // Handle lazy loading
        handleLazyLoad: function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    const adContainer = $(entry.target);
                    MR9Ads.loadAd(adContainer);
                    MR9Ads.state.viewabilityObserver.unobserve(entry.target);
                }
            });
        },
        
        // Load individual ad
        loadAd: function(adContainer) {
            if (adContainer.hasClass('loaded')) return;
            
            const adScript = adContainer.find('script');
            if (adScript.length > 0) {
                try {
                    eval(adScript.html());
                    adContainer.addClass('loaded');
                    this.logAdEvent('loaded', {
                        adId: adContainer.attr('id') || 'unknown'
                    });
                } catch (error) {
                    console.error('Error loading ad:', error);
                }
            }
        },
        
        // Load all ads (fallback)
        loadAllAds: function() {
            $('.ad-container:not(.loaded)').each(function() {
                MR9Ads.loadAd($(this));
            });
        },
        
        // Track user engagement
        trackUserEngagement: function() {
            let lastActivity = Date.now();
            
            $(document).on('scroll mousemove click keypress', function() {
                lastActivity = Date.now();
                MR9Ads.state.userEngaged = true;
            });
            
            // Store last activity
            this.getLastActivity = function() {
                return lastActivity;
            };
        },
        
        // Optimize ad sizes based on viewport
        optimizeAdSizes: function() {
            const viewport = {
                width: $(window).width(),
                height: $(window).height()
            };
            
            // Hide/show ads based on viewport
            if (viewport.width < 768) {
                $('.ad-leaderboard').hide();
                $('.ad-mobile-banner').show();
            } else {
                $('.ad-leaderboard').show();
                $('.ad-mobile-banner').hide();
            }
            
            // Adjust ad container sizes
            this.adjustAdContainerSizes(viewport);
        },
        
        // Adjust ad container sizes
        adjustAdContainerSizes: function(viewport) {
            $('.ad-container').each(function() {
                const container = $(this);
                
                if (container.hasClass('ad-rectangle')) {
                    container.css('min-height', '250px');
                } else if (container.hasClass('ad-leaderboard')) {
                    container.css('min-height', '90px');
                } else if (container.hasClass('ad-mobile-banner')) {
                    container.css('min-height', '50px');
                }
            });
        },
        
        // Log ad events for analytics
        logAdEvent: function(event, data) {
            const logData = {
                event: event,
                timestamp: Date.now(),
                url: window.location.href,
                userAgent: navigator.userAgent,
                viewport: {
                    width: $(window).width(),
                    height: $(window).height()
                },
                ...data
            };
            
            // Send to analytics endpoint if available
            if (typeof mr9_ajax !== 'undefined') {
                $.ajax({
                    url: mr9_ajax.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'mr9_log_ad_event',
                        nonce: mr9_ajax.nonce,
                        log_data: JSON.stringify(logData)
                    }
                });
            }
            
            // Console log for debugging
            if (window.console && console.log) {
                console.log('MR9 Ad Event:', logData);
            }
        },
        
        // Handle window resize
        handleResize: function() {
            clearTimeout(this.resizeTimeout);
            this.resizeTimeout = setTimeout(function() {
                MR9Ads.optimizeAdSizes();
            }, 250);
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        MR9Ads.init();
    });
    
    // Handle window resize
    $(window).on('resize', function() {
        MR9Ads.handleResize();
    });
    
    // Handle page visibility changes
    $(document).on('visibilitychange', function() {
        if (document.hidden) {
            // Pause ad refresh when page is hidden
            MR9Ads.state.userEngaged = false;
        }
    });
    
    // Expose MR9Ads globally for debugging
    window.MR9Ads = MR9Ads;
    
})(jQuery);
