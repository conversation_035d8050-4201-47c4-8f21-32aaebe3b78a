/**
 * Main Theme JavaScript
 * 
 * @package MR9_Theme
 * @version 1.0.0
 */

(function($) {
    'use strict';
    
    // Main theme object
    const MR9Theme = {
        
        // Initialize theme functionality
        init: function() {
            this.setupNavigation();
            this.setupSearch();
            this.setupScrollEffects();
            this.setupImageOptimization();
            this.setupPerformanceMonitoring();
            this.setupAccessibility();
            this.setupSocialSharing();
        },
        
        // Setup mobile navigation
        setupNavigation: function() {
            const menuToggle = $('.menu-toggle');
            const navigation = $('#site-navigation');
            const primaryMenu = $('#primary-menu');
            
            // Mobile menu toggle
            menuToggle.on('click', function(e) {
                e.preventDefault();
                
                const isExpanded = $(this).attr('aria-expanded') === 'true';
                $(this).attr('aria-expanded', !isExpanded);
                
                navigation.toggleClass('menu-open');
                primaryMenu.slideToggle(300);
                
                // Trap focus in mobile menu
                if (!isExpanded) {
                    MR9Theme.trapFocus(navigation[0]);
                }
            });
            
            // Close mobile menu on escape key
            $(document).on('keydown', function(e) {
                if (e.keyCode === 27 && navigation.hasClass('menu-open')) {
                    menuToggle.click();
                }
            });
            
            // Close mobile menu when clicking outside
            $(document).on('click', function(e) {
                if (!$(e.target).closest('#site-navigation').length && 
                    navigation.hasClass('menu-open')) {
                    menuToggle.click();
                }
            });
            
            // Handle submenu accessibility
            primaryMenu.find('li').has('ul').each(function() {
                const link = $(this).children('a');
                link.after('<button class="submenu-toggle" aria-expanded="false"><span class="screen-reader-text">Expand submenu</span></button>');
            });
            
            $('.submenu-toggle').on('click', function(e) {
                e.preventDefault();
                const submenu = $(this).siblings('ul');
                const isExpanded = $(this).attr('aria-expanded') === 'true';
                
                $(this).attr('aria-expanded', !isExpanded);
                submenu.slideToggle(200);
            });
        },
        
        // Setup search functionality
        setupSearch: function() {
            const searchForm = $('.search-form');
            const searchInput = searchForm.find('input[type="search"]');
            
            // Enhanced search with suggestions
            if (searchInput.length > 0) {
                let searchTimeout;
                
                searchInput.on('input', function() {
                    clearTimeout(searchTimeout);
                    const query = $(this).val();
                    
                    if (query.length > 2) {
                        searchTimeout = setTimeout(function() {
                            MR9Theme.fetchSearchSuggestions(query);
                        }, 300);
                    }
                });
            }
        },
        
        // Fetch search suggestions
        fetchSearchSuggestions: function(query) {
            if (typeof mr9_ajax === 'undefined') return;
            
            $.ajax({
                url: mr9_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'mr9_search_suggestions',
                    nonce: mr9_ajax.nonce,
                    query: query
                },
                success: function(response) {
                    if (response.success) {
                        MR9Theme.displaySearchSuggestions(response.data);
                    }
                }
            });
        },
        
        // Display search suggestions
        displaySearchSuggestions: function(suggestions) {
            let suggestionsList = $('.search-suggestions');
            
            if (suggestionsList.length === 0) {
                suggestionsList = $('<ul class="search-suggestions"></ul>');
                $('.search-form').append(suggestionsList);
            }
            
            suggestionsList.empty();
            
            suggestions.forEach(function(suggestion) {
                const item = $('<li><a href="' + suggestion.url + '">' + suggestion.title + '</a></li>');
                suggestionsList.append(item);
            });
        },
        
        // Setup scroll effects
        setupScrollEffects: function() {
            let lastScrollTop = 0;
            const header = $('.site-header');
            
            $(window).on('scroll', function() {
                const scrollTop = $(this).scrollTop();
                
                // Hide/show header on scroll
                if (scrollTop > lastScrollTop && scrollTop > 100) {
                    header.addClass('header-hidden');
                } else {
                    header.removeClass('header-hidden');
                }
                
                lastScrollTop = scrollTop;
                
                // Update reading progress
                MR9Theme.updateReadingProgress();
            });
            
            // Smooth scroll for anchor links
            $('a[href^="#"]').on('click', function(e) {
                const target = $(this.getAttribute('href'));
                
                if (target.length) {
                    e.preventDefault();
                    $('html, body').animate({
                        scrollTop: target.offset().top - 100
                    }, 500);
                }
            });
        },
        
        // Update reading progress
        updateReadingProgress: function() {
            if (!$('body').hasClass('single-post')) return;
            
            const article = $('.post-content');
            if (article.length === 0) return;
            
            const articleTop = article.offset().top;
            const articleHeight = article.outerHeight();
            const windowTop = $(window).scrollTop();
            const windowHeight = $(window).height();
            
            const progress = Math.min(100, Math.max(0, 
                ((windowTop + windowHeight - articleTop) / articleHeight) * 100
            ));
            
            // Update progress bar if it exists
            $('.reading-progress-bar').css('width', progress + '%');
        },
        
        // Setup image optimization
        setupImageOptimization: function() {
            // Lazy load images
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver(function(entries) {
                    entries.forEach(function(entry) {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            img.classList.add('loaded');
                            imageObserver.unobserve(img);
                        }
                    });
                });
                
                $('.lazy').each(function() {
                    imageObserver.observe(this);
                });
            }
            
            // Add loading animation to images
            $('img').on('load', function() {
                $(this).addClass('loaded');
            });
        },
        
        // Setup performance monitoring
        setupPerformanceMonitoring: function() {
            // Monitor Core Web Vitals if enabled
            if (window.performance && window.performance.mark) {
                // Mark important events
                window.performance.mark('theme-js-start');
                
                $(window).on('load', function() {
                    window.performance.mark('theme-js-complete');
                    
                    // Measure performance
                    setTimeout(function() {
                        MR9Theme.measurePerformance();
                    }, 1000);
                });
            }
        },
        
        // Measure performance metrics
        measurePerformance: function() {
            if (!window.performance) return;
            
            const navigation = performance.getEntriesByType('navigation')[0];
            const metrics = {
                domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                firstPaint: 0,
                firstContentfulPaint: 0
            };
            
            // Get paint metrics
            const paintEntries = performance.getEntriesByType('paint');
            paintEntries.forEach(function(entry) {
                if (entry.name === 'first-paint') {
                    metrics.firstPaint = entry.startTime;
                } else if (entry.name === 'first-contentful-paint') {
                    metrics.firstContentfulPaint = entry.startTime;
                }
            });
            
            // Send metrics to analytics
            this.sendPerformanceMetrics(metrics);
        },
        
        // Send performance metrics
        sendPerformanceMetrics: function(metrics) {
            if (typeof gtag !== 'undefined') {
                gtag('event', 'performance_metrics', {
                    event_category: 'Performance',
                    custom_map: {
                        metric_1: 'dom_content_loaded',
                        metric_2: 'load_complete',
                        metric_3: 'first_paint',
                        metric_4: 'first_contentful_paint'
                    },
                    metric_1: metrics.domContentLoaded,
                    metric_2: metrics.loadComplete,
                    metric_3: metrics.firstPaint,
                    metric_4: metrics.firstContentfulPaint
                });
            }
        },
        
        // Setup accessibility features
        setupAccessibility: function() {
            // Skip link functionality
            $('.skip-link').on('click', function(e) {
                const target = $($(this).attr('href'));
                if (target.length) {
                    target.attr('tabindex', '-1').focus();
                }
            });
            
            // Keyboard navigation for dropdowns
            $('.menu-item-has-children > a').on('keydown', function(e) {
                if (e.keyCode === 40) { // Down arrow
                    e.preventDefault();
                    $(this).siblings('ul').find('a').first().focus();
                }
            });
            
            // Focus management
            this.manageFocus();
        },
        
        // Manage focus for accessibility
        manageFocus: function() {
            // Focus visible elements only
            $('a, button, input, textarea, select').on('focus', function() {
                if (!$(this).is(':visible')) {
                    $(this).blur();
                }
            });
        },
        
        // Trap focus within element
        trapFocus: function(element) {
            const focusableElements = element.querySelectorAll(
                'a[href], button, textarea, input[type="text"], input[type="radio"], input[type="checkbox"], select'
            );
            
            const firstFocusableElement = focusableElements[0];
            const lastFocusableElement = focusableElements[focusableElements.length - 1];
            
            element.addEventListener('keydown', function(e) {
                if (e.keyCode === 9) { // Tab key
                    if (e.shiftKey) {
                        if (document.activeElement === firstFocusableElement) {
                            lastFocusableElement.focus();
                            e.preventDefault();
                        }
                    } else {
                        if (document.activeElement === lastFocusableElement) {
                            firstFocusableElement.focus();
                            e.preventDefault();
                        }
                    }
                }
            });
        },
        
        // Setup social sharing
        setupSocialSharing: function() {
            $('.social-share-button').on('click', function(e) {
                const url = $(this).attr('href');
                
                if (url && url.startsWith('http')) {
                    e.preventDefault();
                    
                    window.open(url, 'share', 
                        'width=600,height=400,scrollbars=yes,resizable=yes'
                    );
                }
            });
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        MR9Theme.init();
    });
    
    // Expose theme object globally
    window.MR9Theme = MR9Theme;
    
})(jQuery);
