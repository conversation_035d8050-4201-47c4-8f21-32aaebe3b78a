/* Critical CSS for above-the-fold content */

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    line-height: 1.6;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    color: #333;
    background-color: #f8f9fa;
}

/* Header styles */
.site-header {
    background: #fff;
    border-bottom: 1px solid #e1e5e9;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.site-logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
    text-decoration: none;
}

/* Navigation */
.main-navigation ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.main-navigation a {
    font-weight: 500;
    color: #333;
    text-decoration: none;
    padding: 0.5rem 0;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.main-navigation a:hover {
    color: #007cba;
    border-bottom-color: #007cba;
}

/* Layout */
.site-container {
    max-width: 1200px;
    margin: 0 auto;
    background: #fff;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

.site-main {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 2rem;
    padding: 2rem;
    min-height: calc(100vh - 200px);
}

.content-area {
    min-width: 0;
}

/* Ad containers - prevent layout shift */
.ad-container {
    margin: 2rem 0;
    text-align: center;
    position: relative;
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ad-leaderboard {
    min-height: 90px;
    max-width: 728px;
    margin: 0 auto;
}

.ad-rectangle {
    min-height: 250px;
    max-width: 300px;
    margin: 0 auto;
}

.ad-mobile-banner {
    min-height: 50px;
    max-width: 320px;
    margin: 0 auto;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
    color: #1a1a1a;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }

/* Post styles */
.post {
    background: #fff;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.post-title {
    font-size: 2.2rem;
    line-height: 1.2;
    margin-bottom: 1rem;
    color: #1a1a1a;
}

.post-meta {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

/* Mobile styles */
@media (max-width: 768px) {
    .site-main {
        grid-template-columns: 1fr;
        padding: 1rem;
        gap: 1rem;
    }
    
    .header-container {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }
    
    .main-navigation ul {
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
    }
    
    .post {
        padding: 1.5rem;
    }
    
    .post-title {
        font-size: 1.8rem;
    }
    
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    
    .ad-leaderboard {
        display: none;
    }
    
    .ad-mobile-banner {
        display: block;
    }
}

@media (min-width: 769px) {
    .ad-mobile-banner {
        display: none;
    }
    
    .ad-leaderboard {
        display: block;
    }
}

/* Loading states */
.lazy-load {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.lazy-load.loaded {
    opacity: 1;
}

/* Skip link for accessibility */
.skip-link {
    position: absolute;
    left: -9999px;
    top: 6px;
    z-index: 999999;
    text-decoration: none;
    background: #000;
    color: #fff;
    padding: 8px 16px;
}

.skip-link:focus {
    left: 6px;
}
