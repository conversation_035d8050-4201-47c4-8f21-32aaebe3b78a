<?php
/**
 * Theme Options and Helper Functions
 *
 * @package MR9_Theme
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Calculate reading time for posts
 */
function mr9_reading_time($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }

    $content = get_post_field('post_content', $post_id);
    $word_count = str_word_count(strip_tags($content));
    $reading_time = ceil($word_count / 200); // Average reading speed: 200 words per minute

    return max(1, $reading_time);
}

/**
 * Get related posts based on categories and tags
 */
function mr9_get_related_posts($post_id = null, $limit = 4) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }

    $categories = wp_get_post_categories($post_id);
    $tags = wp_get_post_tags($post_id);

    $args = array(
        'post_type' => 'post',
        'post_status' => 'publish',
        'posts_per_page' => $limit,
        'post__not_in' => array($post_id),
        'orderby' => 'rand',
        'meta_query' => array(
            array(
                'key' => '_thumbnail_id',
                'compare' => 'EXISTS'
            )
        )
    );

    // First try to get posts from same categories
    if (!empty($categories)) {
        $args['category__in'] = $categories;
        $related_posts = get_posts($args);

        if (count($related_posts) >= $limit) {
            return $related_posts;
        }
    }

    // If not enough posts, try tags
    if (!empty($tags) && count($related_posts) < $limit) {
        $tag_ids = array();
        foreach ($tags as $tag) {
            $tag_ids[] = $tag->term_id;
        }

        $args['tag__in'] = $tag_ids;
        $args['posts_per_page'] = $limit - count($related_posts);
        unset($args['category__in']);

        $tag_posts = get_posts($args);
        $related_posts = array_merge($related_posts, $tag_posts);
    }

    // If still not enough, get recent posts
    if (count($related_posts) < $limit) {
        $args = array(
            'post_type' => 'post',
            'post_status' => 'publish',
            'posts_per_page' => $limit - count($related_posts),
            'post__not_in' => array($post_id),
            'orderby' => 'date',
            'order' => 'DESC'
        );

        $recent_posts = get_posts($args);
        $related_posts = array_merge($related_posts, $recent_posts);
    }

    return array_slice($related_posts, 0, $limit);
}

/**
 * Get popular posts based on views
 */
function mr9_get_popular_posts($limit = 5) {
    $args = array(
        'post_type' => 'post',
        'post_status' => 'publish',
        'posts_per_page' => $limit,
        'meta_key' => 'mr9_post_views',
        'orderby' => 'meta_value_num',
        'order' => 'DESC',
        'date_query' => array(
            array(
                'after' => '30 days ago'
            )
        )
    );

    return get_posts($args);
}

/**
 * Track post views
 */
function mr9_track_post_views($post_id) {
    if (!is_single()) return;
    if (empty($post_id)) {
        global $post;
        $post_id = $post->ID;
    }

    // Don't count views from bots or logged-in admins
    if (is_admin() || current_user_can('manage_options')) return;

    $views = get_post_meta($post_id, 'mr9_post_views', true);
    $views = empty($views) ? 1 : $views + 1;

    update_post_meta($post_id, 'mr9_post_views', $views);
}
add_action('wp_head', 'mr9_track_post_views');

/**
 * Get post views count
 */
function mr9_get_post_views($post_id) {
    $views = get_post_meta($post_id, 'mr9_post_views', true);
    return empty($views) ? 0 : $views;
}

/**
 * Breadcrumbs function
 */
function mr9_breadcrumbs() {
    if (is_front_page()) return;

    $separator = ' &raquo; ';
    $home_title = __('Home', 'mr9-theme');

    echo '<nav class="breadcrumbs" aria-label="' . esc_attr__('Breadcrumb Navigation', 'mr9-theme') . '">';
    echo '<a href="' . esc_url(home_url('/')) . '">' . $home_title . '</a>' . $separator;

    if (is_category() || is_single()) {
        $category = get_the_category();
        if (!empty($category)) {
            $category = $category[0];
            echo '<a href="' . esc_url(get_category_link($category->term_id)) . '">' . esc_html($category->name) . '</a>';
            if (is_single()) {
                echo $separator . '<span class="current">' . get_the_title() . '</span>';
            }
        }
    } elseif (is_page()) {
        if ($post->post_parent) {
            $ancestors = get_post_ancestors($post->ID);
            $ancestors = array_reverse($ancestors);

            foreach ($ancestors as $ancestor) {
                echo '<a href="' . esc_url(get_permalink($ancestor)) . '">' . get_the_title($ancestor) . '</a>' . $separator;
            }
        }
        echo '<span class="current">' . get_the_title() . '</span>';
    } elseif (is_tag()) {
        echo '<span class="current">' . single_tag_title('', false) . '</span>';
    } elseif (is_author()) {
        echo '<span class="current">' . get_the_author() . '</span>';
    } elseif (is_404()) {
        echo '<span class="current">' . __('404 Not Found', 'mr9-theme') . '</span>';
    } elseif (is_search()) {
        echo '<span class="current">' . __('Search Results', 'mr9-theme') . '</span>';
    }

    echo '</nav>';
}

/**
 * Fallback menu for primary navigation
 */
function mr9_fallback_menu() {
    echo '<ul id="primary-menu">';
    echo '<li><a href="' . esc_url(home_url('/')) . '">' . __('Home', 'mr9-theme') . '</a></li>';

    $pages = get_pages(array('sort_column' => 'menu_order'));
    foreach ($pages as $page) {
        echo '<li><a href="' . esc_url(get_permalink($page->ID)) . '">' . esc_html($page->post_title) . '</a></li>';
    }

    echo '</ul>';
}

/**
 * Custom excerpt with more control
 */
function mr9_custom_excerpt($limit = 25, $post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }

    $excerpt = get_post_field('post_excerpt', $post_id);

    if (empty($excerpt)) {
        $content = get_post_field('post_content', $post_id);
        $excerpt = wp_strip_all_tags($content);
    }

    return wp_trim_words($excerpt, $limit, '...');
}

/**
 * Get estimated earnings per post (for analytics)
 */
function mr9_get_post_earnings($post_id) {
    $views = mr9_get_post_views($post_id);
    $estimated_ctr = 0.02; // 2% CTR estimate
    $estimated_cpc = 0.50; // $0.50 CPC estimate

    return round($views * $estimated_ctr * $estimated_cpc, 2);
}

/**
 * Check if post has good ad performance potential
 */
function mr9_has_ad_potential($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }

    $word_count = str_word_count(strip_tags(get_post_field('post_content', $post_id)));
    $has_thumbnail = has_post_thumbnail($post_id);
    $views = mr9_get_post_views($post_id);

    // Good ad potential if: long content, has thumbnail, and getting views
    return ($word_count > 500 && $has_thumbnail && $views > 10);
}

/**
 * Get theme performance metrics
 */
function mr9_get_performance_metrics() {
    return array(
        'total_posts' => wp_count_posts()->publish,
        'total_views' => mr9_get_total_views(),
        'avg_reading_time' => mr9_get_average_reading_time(),
        'top_performing_posts' => mr9_get_popular_posts(5)
    );
}

/**
 * Get total site views
 */
function mr9_get_total_views() {
    global $wpdb;

    $total_views = $wpdb->get_var("
        SELECT SUM(meta_value)
        FROM {$wpdb->postmeta}
        WHERE meta_key = 'mr9_post_views'
    ");

    return $total_views ? $total_views : 0;
}

/**
 * Get average reading time across all posts
 */
function mr9_get_average_reading_time() {
    $posts = get_posts(array(
        'numberposts' => -1,
        'post_status' => 'publish'
    ));

    $total_time = 0;
    foreach ($posts as $post) {
        $total_time += mr9_reading_time($post->ID);
    }

    return count($posts) > 0 ? round($total_time / count($posts), 1) : 0;
}

/**
 * AJAX handler for search suggestions
 */
function mr9_ajax_search_suggestions() {
    check_ajax_referer('mr9_nonce', 'nonce');

    $query = sanitize_text_field($_POST['query']);

    if (strlen($query) < 3) {
        wp_die();
    }

    $posts = get_posts(array(
        'post_type' => 'post',
        'post_status' => 'publish',
        'posts_per_page' => 5,
        's' => $query
    ));

    $suggestions = array();
    foreach ($posts as $post) {
        $suggestions[] = array(
            'title' => $post->post_title,
            'url' => get_permalink($post->ID)
        );
    }

    wp_send_json_success($suggestions);
}
add_action('wp_ajax_mr9_search_suggestions', 'mr9_ajax_search_suggestions');
add_action('wp_ajax_nopriv_mr9_search_suggestions', 'mr9_ajax_search_suggestions');

/**
 * AJAX handler for ad event logging
 */
function mr9_ajax_log_ad_event() {
    check_ajax_referer('mr9_nonce', 'nonce');

    $log_data = json_decode(stripslashes($_POST['log_data']), true);

    // Store ad events in database for analytics
    global $wpdb;

    $table_name = $wpdb->prefix . 'mr9_ad_events';

    // Create table if it doesn't exist
    $charset_collate = $wpdb->get_charset_collate();
    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        event_type varchar(50) NOT NULL,
        event_data longtext NOT NULL,
        timestamp datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);

    // Insert event data
    $wpdb->insert(
        $table_name,
        array(
            'event_type' => $log_data['event'],
            'event_data' => json_encode($log_data)
        )
    );

    wp_send_json_success();
}
add_action('wp_ajax_mr9_log_ad_event', 'mr9_ajax_log_ad_event');
add_action('wp_ajax_nopriv_mr9_log_ad_event', 'mr9_ajax_log_ad_event');

/**
 * Get ad performance analytics
 */
function mr9_get_ad_analytics($days = 30) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'mr9_ad_events';
    $date_limit = date('Y-m-d H:i:s', strtotime("-{$days} days"));

    $results = $wpdb->get_results($wpdb->prepare(
        "SELECT event_type, COUNT(*) as count
         FROM $table_name
         WHERE timestamp >= %s
         GROUP BY event_type",
        $date_limit
    ));

    $analytics = array();
    foreach ($results as $result) {
        $analytics[$result->event_type] = $result->count;
    }

    return $analytics;
}

/**
 * Display admin dashboard widget with ad performance
 */
function mr9_add_dashboard_widget() {
    wp_add_dashboard_widget(
        'mr9_ad_performance',
        __('Ad Performance', 'mr9-theme'),
        'mr9_dashboard_widget_content'
    );
}
add_action('wp_dashboard_setup', 'mr9_add_dashboard_widget');

/**
 * Dashboard widget content
 */
function mr9_dashboard_widget_content() {
    $analytics = mr9_get_ad_analytics(7); // Last 7 days
    $total_views = mr9_get_total_views();
    $performance_metrics = mr9_get_performance_metrics();

    echo '<div class="mr9-dashboard-widget">';
    echo '<h4>' . __('Last 7 Days', 'mr9-theme') . '</h4>';
    echo '<p><strong>' . __('Total Page Views:', 'mr9-theme') . '</strong> ' . number_format($total_views) . '</p>';
    echo '<p><strong>' . __('Ad Loads:', 'mr9-theme') . '</strong> ' . (isset($analytics['loaded']) ? $analytics['loaded'] : 0) . '</p>';
    echo '<p><strong>' . __('Ad Refreshes:', 'mr9-theme') . '</strong> ' . (isset($analytics['refresh']) ? $analytics['refresh'] : 0) . '</p>';
    echo '<p><strong>' . __('Viewability Events:', 'mr9-theme') . '</strong> ' . (isset($analytics['viewability']) ? $analytics['viewability'] : 0) . '</p>';

    if ($total_views > 0 && isset($analytics['loaded'])) {
        $ad_coverage = round(($analytics['loaded'] / $total_views) * 100, 1);
        echo '<p><strong>' . __('Ad Coverage:', 'mr9-theme') . '</strong> ' . $ad_coverage . '%</p>';
    }

    echo '<p><a href="' . admin_url('customize.php?autofocus[panel]=mr9_adsense_panel') . '" class="button button-primary">' . __('Configure Ads', 'mr9-theme') . '</a></p>';
    echo '</div>';
}

/**
 * Add admin menu for theme analytics
 */
function mr9_add_admin_menu() {
    add_theme_page(
        __('MR9 Analytics', 'mr9-theme'),
        __('MR9 Analytics', 'mr9-theme'),
        'manage_options',
        'mr9-analytics',
        'mr9_analytics_page'
    );
}
add_action('admin_menu', 'mr9_add_admin_menu');

/**
 * Analytics admin page
 */
function mr9_analytics_page() {
    $analytics_30 = mr9_get_ad_analytics(30);
    $performance = mr9_get_performance_metrics();

    echo '<div class="wrap">';
    echo '<h1>' . __('MR9 Theme Analytics', 'mr9-theme') . '</h1>';

    echo '<div class="mr9-analytics-grid">';

    // Performance metrics
    echo '<div class="mr9-analytics-card">';
    echo '<h3>' . __('Performance Metrics', 'mr9-theme') . '</h3>';
    echo '<p><strong>' . __('Total Posts:', 'mr9-theme') . '</strong> ' . $performance['total_posts'] . '</p>';
    echo '<p><strong>' . __('Total Views:', 'mr9-theme') . '</strong> ' . number_format($performance['total_views']) . '</p>';
    echo '<p><strong>' . __('Average Reading Time:', 'mr9-theme') . '</strong> ' . $performance['avg_reading_time'] . ' min</p>';
    echo '</div>';

    // Ad performance
    echo '<div class="mr9-analytics-card">';
    echo '<h3>' . __('Ad Performance (30 Days)', 'mr9-theme') . '</h3>';
    foreach ($analytics_30 as $event_type => $count) {
        echo '<p><strong>' . ucfirst($event_type) . ':</strong> ' . number_format($count) . '</p>';
    }
    echo '</div>';

    echo '</div>';
    echo '</div>';

    // Add some basic CSS
    echo '<style>
        .mr9-analytics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 20px; }
        .mr9-analytics-card { background: #fff; padding: 20px; border: 1px solid #ccd0d4; box-shadow: 0 1px 1px rgba(0,0,0,.04); }
        .mr9-analytics-card h3 { margin-top: 0; }
    </style>';
}
