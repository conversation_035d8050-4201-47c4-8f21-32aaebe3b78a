<?php
/**
 * Performance Optimization Functions
 *
 * @package MR9_Theme
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Optimize images for better Core Web Vitals
 */
function mr9_optimize_images($attr, $attachment, $size) {
    // Add loading="lazy" to images except above-the-fold
    if (!is_admin() && !wp_is_mobile()) {
        $attr['loading'] = 'lazy';
    }
    
    // Add decoding="async" for better performance
    $attr['decoding'] = 'async';
    
    return $attr;
}
add_filter('wp_get_attachment_image_attributes', 'mr9_optimize_images', 10, 3);

/**
 * Preload critical resources
 */
function mr9_preload_critical_resources() {
    // Preload critical CSS
    echo '<link rel="preload" href="' . get_stylesheet_uri() . '" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">' . "\n";
    
    // Preload Google Fonts if used
    if (get_theme_mod('mr9_google_fonts', false)) {
        echo '<link rel="preconnect" href="https://fonts.googleapis.com">' . "\n";
        echo '<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>' . "\n";
    }
    
    // Preload AdSense resources
    echo '<link rel="preconnect" href="https://pagead2.googlesyndication.com">' . "\n";
    echo '<link rel="preconnect" href="https://googleads.g.doubleclick.net">' . "\n";
    echo '<link rel="dns-prefetch" href="//www.googletagservices.com">' . "\n";
    
    // Preload hero image on homepage
    if (is_front_page() && has_post_thumbnail()) {
        $hero_image = wp_get_attachment_image_url(get_post_thumbnail_id(), 'large');
        echo '<link rel="preload" href="' . esc_url($hero_image) . '" as="image">' . "\n";
    }
}
add_action('wp_head', 'mr9_preload_critical_resources', 1);

/**
 * Optimize database queries
 */
function mr9_optimize_queries() {
    // Remove unnecessary queries
    remove_action('wp_head', 'wp_generator');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'rsd_link');
    remove_action('wp_head', 'wp_shortlink_wp_head');
    
    // Disable pingbacks
    add_filter('xmlrpc_enabled', '__return_false');
    
    // Remove query strings from static resources
    add_filter('script_loader_src', 'mr9_remove_query_strings', 15, 1);
    add_filter('style_loader_src', 'mr9_remove_query_strings', 15, 1);
}
add_action('init', 'mr9_optimize_queries');

/**
 * Remove query strings from static resources
 */
function mr9_remove_query_strings($src) {
    if (strpos($src, 'ver=')) {
        $src = remove_query_arg('ver', $src);
    }
    return $src;
}

/**
 * Optimize CSS delivery
 */
function mr9_optimize_css_delivery() {
    // Critical CSS inline
    $critical_css_file = get_template_directory() . '/assets/css/critical.css';
    if (file_exists($critical_css_file)) {
        echo '<style id="critical-css">' . file_get_contents($critical_css_file) . '</style>';
    }
    
    // Load non-critical CSS asynchronously
    echo '<script>
        function loadCSS(href) {
            var link = document.createElement("link");
            link.rel = "stylesheet";
            link.href = href;
            document.head.appendChild(link);
        }
        
        window.addEventListener("load", function() {
            loadCSS("' . get_stylesheet_uri() . '");
        });
    </script>';
}

/**
 * Implement resource hints
 */
function mr9_add_resource_hints($urls, $relation_type) {
    switch ($relation_type) {
        case 'dns-prefetch':
            $urls[] = '//fonts.googleapis.com';
            $urls[] = '//fonts.gstatic.com';
            $urls[] = '//pagead2.googlesyndication.com';
            $urls[] = '//www.googletagservices.com';
            break;
            
        case 'preconnect':
            $urls[] = 'https://fonts.gstatic.com';
            $urls[] = 'https://pagead2.googlesyndication.com';
            break;
    }
    
    return $urls;
}
add_filter('wp_resource_hints', 'mr9_add_resource_hints', 10, 2);

/**
 * Optimize JavaScript loading
 */
function mr9_optimize_javascript() {
    // Defer non-critical JavaScript
    add_filter('script_loader_tag', function($tag, $handle) {
        $defer_scripts = array('mr9-main', 'comment-reply');
        
        if (in_array($handle, $defer_scripts)) {
            return str_replace('<script ', '<script defer ', $tag);
        }
        
        return $tag;
    }, 10, 2);
}
add_action('wp_enqueue_scripts', 'mr9_optimize_javascript');

/**
 * Implement lazy loading for iframes and embeds
 */
function mr9_lazy_load_embeds($html, $url, $attr, $post_id) {
    // Add loading="lazy" to iframes
    if (strpos($html, '<iframe') !== false) {
        $html = str_replace('<iframe', '<iframe loading="lazy"', $html);
    }
    
    return $html;
}
add_filter('embed_oembed_html', 'mr9_lazy_load_embeds', 10, 4);

/**
 * Optimize WordPress heartbeat
 */
function mr9_optimize_heartbeat() {
    // Disable heartbeat on frontend
    if (!is_admin()) {
        wp_deregister_script('heartbeat');
    }
}
add_action('init', 'mr9_optimize_heartbeat');

/**
 * Cache optimization for better performance
 */
function mr9_cache_optimization() {
    // Set cache headers for static assets
    if (!is_admin()) {
        header('Cache-Control: public, max-age=31536000');
        header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 31536000) . ' GMT');
    }
}

/**
 * Optimize database for better performance
 */
function mr9_optimize_database() {
    // Clean up post revisions
    if (get_theme_mod('mr9_cleanup_revisions', false)) {
        global $wpdb;
        $wpdb->query("DELETE FROM {$wpdb->posts} WHERE post_type = 'revision'");
    }
    
    // Clean up spam comments
    if (get_theme_mod('mr9_cleanup_spam', false)) {
        global $wpdb;
        $wpdb->query("DELETE FROM {$wpdb->comments} WHERE comment_approved = 'spam'");
    }
    
    // Clean up transients
    if (get_theme_mod('mr9_cleanup_transients', false)) {
        global $wpdb;
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_%'");
    }
}

/**
 * Implement service worker for caching
 */
function mr9_add_service_worker() {
    if (get_theme_mod('mr9_enable_service_worker', false)) {
        echo '<script>
            if ("serviceWorker" in navigator) {
                window.addEventListener("load", function() {
                    navigator.serviceWorker.register("/sw.js")
                        .then(function(registration) {
                            console.log("SW registered: ", registration);
                        })
                        .catch(function(registrationError) {
                            console.log("SW registration failed: ", registrationError);
                        });
                });
            }
        </script>';
    }
}
add_action('wp_footer', 'mr9_add_service_worker');

/**
 * Optimize Core Web Vitals
 */
function mr9_optimize_core_web_vitals() {
    // Reduce Cumulative Layout Shift (CLS)
    echo '<style>
        /* Reserve space for ads to prevent layout shift */
        .ad-container {
            min-height: 250px;
            background: #f8f9fa;
        }
        
        .ad-leaderboard {
            min-height: 90px;
        }
        
        .ad-rectangle {
            min-height: 250px;
        }
        
        .ad-skyscraper {
            min-height: 600px;
        }
        
        /* Optimize font loading */
        @font-face {
            font-family: "System";
            src: local("system-ui"), local("-apple-system"), local("BlinkMacSystemFont");
            font-display: swap;
        }
    </style>';
}
add_action('wp_head', 'mr9_optimize_core_web_vitals');

/**
 * Monitor Core Web Vitals
 */
function mr9_monitor_core_web_vitals() {
    if (get_theme_mod('mr9_monitor_vitals', false)) {
        echo '<script>
            function sendToAnalytics(metric) {
                // Send to Google Analytics or other analytics service
                if (typeof gtag !== "undefined") {
                    gtag("event", metric.name, {
                        event_category: "Web Vitals",
                        event_label: metric.id,
                        value: Math.round(metric.name === "CLS" ? metric.value * 1000 : metric.value),
                        non_interaction: true,
                    });
                }
            }
            
            // Load web-vitals library
            import("https://unpkg.com/web-vitals").then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
                getCLS(sendToAnalytics);
                getFID(sendToAnalytics);
                getFCP(sendToAnalytics);
                getLCP(sendToAnalytics);
                getTTFB(sendToAnalytics);
            });
        </script>';
    }
}
add_action('wp_footer', 'mr9_monitor_core_web_vitals');

/**
 * Optimize for mobile performance
 */
function mr9_mobile_optimization() {
    if (wp_is_mobile()) {
        // Reduce image quality for mobile
        add_filter('jpeg_quality', function() { return 75; });
        
        // Disable certain features on mobile
        remove_action('wp_head', 'print_emoji_detection_script', 7);
        remove_action('wp_print_styles', 'print_emoji_styles');
    }
}
add_action('init', 'mr9_mobile_optimization');
