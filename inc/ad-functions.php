<?php
/**
 * AdSense Functions and Optimization
 *
 * @package MR9_Theme
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Check if ads should be displayed
 */
function mr9_should_display_ads() {
    // Don't show ads to logged-in administrators
    if (current_user_can('manage_options')) {
        return false;
    }
    
    // Don't show ads on 404 pages
    if (is_404()) {
        return false;
    }
    
    // Check if ads are globally disabled
    if (!get_theme_mod('mr9_enable_ads', true)) {
        return false;
    }
    
    return true;
}

/**
 * Get AdSense client ID
 */
function mr9_get_adsense_client() {
    return get_theme_mod('mr9_adsense_client_id', '');
}

/**
 * Generate AdSense ad unit
 */
function mr9_display_adsense_ad($slot_id, $size_class = 'ad-rectangle', $format = 'auto') {
    if (!mr9_should_display_ads()) {
        return;
    }
    
    $client_id = mr9_get_adsense_client();
    if (empty($client_id) || empty($slot_id)) {
        return;
    }
    
    $ad_id = 'ad-' . uniqid();
    ?>
    <div class="ad-container <?php echo esc_attr($size_class); ?>" role="complementary" aria-label="Publicidade">
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-client="<?php echo esc_attr($client_id); ?>"
             data-ad-slot="<?php echo esc_attr($slot_id); ?>"
             data-ad-format="<?php echo esc_attr($format); ?>"
             data-full-width-responsive="true"
             id="<?php echo esc_attr($ad_id); ?>"></ins>
        <script>
            (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
    </div>
    <?php
}

/**
 * Display responsive ad based on device
 */
function mr9_display_responsive_ad($desktop_slot, $mobile_slot, $size_class = 'ad-rectangle') {
    if (!mr9_should_display_ads()) {
        return;
    }
    
    if (wp_is_mobile()) {
        mr9_display_adsense_ad($mobile_slot, $size_class . ' ad-mobile');
    } else {
        mr9_display_adsense_ad($desktop_slot, $size_class . ' ad-desktop');
    }
}

/**
 * Display native ad
 */
function mr9_display_native_ad($slot_id) {
    if (!mr9_should_display_ads()) {
        return;
    }
    
    $client_id = mr9_get_adsense_client();
    if (empty($client_id) || empty($slot_id)) {
        return;
    }
    ?>
    <div class="ad-container ad-native" role="complementary" aria-label="Publicidade">
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-format="fluid"
             data-ad-layout-key="-6t+ed+2i-1n-4w"
             data-ad-client="<?php echo esc_attr($client_id); ?>"
             data-ad-slot="<?php echo esc_attr($slot_id); ?>"></ins>
        <script>
            (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
    </div>
    <?php
}

/**
 * Display multiplex ad
 */
function mr9_display_multiplex_ad($slot_id) {
    if (!mr9_should_display_ads()) {
        return;
    }
    
    $client_id = mr9_get_adsense_client();
    if (empty($client_id) || empty($slot_id)) {
        return;
    }
    ?>
    <div class="ad-container ad-multiplex" role="complementary" aria-label="Publicidade">
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-format="autorelaxed"
             data-ad-client="<?php echo esc_attr($client_id); ?>"
             data-ad-slot="<?php echo esc_attr($slot_id); ?>"></ins>
        <script>
            (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
    </div>
    <?php
}

/**
 * Display in-article ad
 */
function mr9_display_in_article_ad($slot_id) {
    if (!mr9_should_display_ads()) {
        return;
    }
    
    $client_id = mr9_get_adsense_client();
    if (empty($client_id) || empty($slot_id)) {
        return;
    }
    ?>
    <div class="ad-container ad-in-article" role="complementary" aria-label="Publicidade">
        <ins class="adsbygoogle"
             style="display:block; text-align:center;"
             data-ad-layout="in-article"
             data-ad-format="fluid"
             data-ad-client="<?php echo esc_attr($client_id); ?>"
             data-ad-slot="<?php echo esc_attr($slot_id); ?>"></ins>
        <script>
            (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
    </div>
    <?php
}

/**
 * Display sticky mobile ad
 */
function mr9_display_sticky_mobile_ad($slot_id) {
    if (!mr9_should_display_ads() || !wp_is_mobile()) {
        return;
    }
    
    $client_id = mr9_get_adsense_client();
    if (empty($client_id) || empty($slot_id)) {
        return;
    }
    ?>
    <div class="ad-sticky" id="sticky-mobile-ad" role="complementary" aria-label="Publicidade">
        <div class="ad-container ad-mobile-banner">
            <button class="ad-close" onclick="mr9CloseAd('sticky-mobile-ad')">&times;</button>
            <ins class="adsbygoogle"
                 style="display:inline-block;width:320px;height:50px"
                 data-ad-client="<?php echo esc_attr($client_id); ?>"
                 data-ad-slot="<?php echo esc_attr($slot_id); ?>"></ins>
            <script>
                (adsbygoogle = window.adsbygoogle || []).push({});
            </script>
        </div>
    </div>
    <?php
}

/**
 * Ad refresh functionality for better RPM
 */
function mr9_add_ad_refresh_script() {
    if (!mr9_should_display_ads()) {
        return;
    }
    ?>
    <script>
    // Ad refresh after user engagement
    let adRefreshTimer;
    let userEngaged = false;
    
    function startAdRefresh() {
        if (userEngaged) return;
        
        // Refresh ads after 30 seconds of engagement
        adRefreshTimer = setTimeout(function() {
            if (typeof googletag !== 'undefined') {
                googletag.pubads().refresh();
            }
        }, 30000);
    }
    
    // Track user engagement
    document.addEventListener('scroll', function() {
        if (!userEngaged) {
            userEngaged = true;
            startAdRefresh();
        }
    });
    
    document.addEventListener('click', function() {
        if (!userEngaged) {
            userEngaged = true;
            startAdRefresh();
        }
    });
    </script>
    <?php
}
add_action('wp_footer', 'mr9_add_ad_refresh_script');

/**
 * Lazy load ads for better performance
 */
function mr9_add_lazy_load_ads_script() {
    ?>
    <script>
    // Lazy load ads when they come into viewport
    function mr9LazyLoadAds() {
        const adContainers = document.querySelectorAll('.ad-container:not(.loaded)');
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    const adContainer = entry.target;
                    const adScript = adContainer.querySelector('script');
                    
                    if (adScript && !adContainer.classList.contains('loaded')) {
                        adContainer.classList.add('loaded');
                        eval(adScript.innerHTML);
                    }
                    
                    observer.unobserve(adContainer);
                }
            });
        }, {
            rootMargin: '100px'
        });
        
        adContainers.forEach(function(container) {
            observer.observe(container);
        });
    }
    
    // Initialize lazy loading when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', mr9LazyLoadAds);
    } else {
        mr9LazyLoadAds();
    }
    </script>
    <?php
}
add_action('wp_footer', 'mr9_add_lazy_load_ads_script');

/**
 * Close ad functionality
 */
function mr9_add_close_ad_script() {
    ?>
    <script>
    function mr9CloseAd(adId) {
        const ad = document.getElementById(adId);
        if (ad) {
            ad.style.display = 'none';
            // Store in localStorage to remember user preference
            localStorage.setItem('mr9_closed_' + adId, 'true');
        }
    }
    
    // Check if user previously closed ads
    document.addEventListener('DOMContentLoaded', function() {
        const stickyAd = document.getElementById('sticky-mobile-ad');
        if (stickyAd && localStorage.getItem('mr9_closed_sticky-mobile-ad') === 'true') {
            stickyAd.style.display = 'none';
        }
    });
    </script>
    <?php
}
add_action('wp_footer', 'mr9_add_close_ad_script');
