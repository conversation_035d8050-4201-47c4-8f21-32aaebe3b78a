<?php
/**
 * SEO Functions and Optimization
 *
 * @package MR9_Theme
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add structured data for better SEO
 */
function mr9_add_structured_data() {
    if (is_single()) {
        global $post;
        
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'Article',
            'headline' => get_the_title(),
            'description' => mr9_custom_excerpt(160),
            'author' => array(
                '@type' => 'Person',
                'name' => get_the_author(),
                'url' => get_author_posts_url(get_the_author_meta('ID'))
            ),
            'publisher' => array(
                '@type' => 'Organization',
                'name' => get_bloginfo('name'),
                'url' => home_url(),
                'logo' => array(
                    '@type' => 'ImageObject',
                    'url' => get_site_icon_url(512)
                )
            ),
            'datePublished' => get_the_date('c'),
            'dateModified' => get_the_modified_date('c'),
            'mainEntityOfPage' => array(
                '@type' => 'WebPage',
                '@id' => get_permalink()
            ),
            'wordCount' => str_word_count(strip_tags(get_the_content())),
            'articleSection' => implode(', ', wp_get_post_categories(get_the_ID(), array('fields' => 'names'))),
            'keywords' => implode(', ', wp_get_post_tags(get_the_ID(), array('fields' => 'names')))
        );
        
        // Add featured image
        if (has_post_thumbnail()) {
            $image_id = get_post_thumbnail_id();
            $image_url = wp_get_attachment_image_url($image_id, 'large');
            $image_meta = wp_get_attachment_metadata($image_id);
            
            $schema['image'] = array(
                '@type' => 'ImageObject',
                'url' => $image_url,
                'width' => $image_meta['width'],
                'height' => $image_meta['height']
            );
        }
        
        // Add reading time
        $schema['timeRequired'] = 'PT' . mr9_reading_time() . 'M';
        
        echo '<script type="application/ld+json">' . json_encode($schema, JSON_UNESCAPED_SLASHES) . '</script>' . "\n";
    }
    
    // Organization schema for homepage
    if (is_front_page()) {
        $organization_schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => get_bloginfo('name'),
            'description' => get_bloginfo('description'),
            'url' => home_url(),
            'logo' => get_site_icon_url(512),
            'sameAs' => mr9_get_social_profiles()
        );
        
        echo '<script type="application/ld+json">' . json_encode($organization_schema, JSON_UNESCAPED_SLASHES) . '</script>' . "\n";
    }
    
    // Website schema
    if (is_front_page()) {
        $website_schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'WebSite',
            'name' => get_bloginfo('name'),
            'description' => get_bloginfo('description'),
            'url' => home_url(),
            'potentialAction' => array(
                '@type' => 'SearchAction',
                'target' => array(
                    '@type' => 'EntryPoint',
                    'urlTemplate' => home_url('/?s={search_term_string}')
                ),
                'query-input' => 'required name=search_term_string'
            )
        );
        
        echo '<script type="application/ld+json">' . json_encode($website_schema, JSON_UNESCAPED_SLASHES) . '</script>' . "\n";
    }
}
add_action('wp_head', 'mr9_add_structured_data');

/**
 * Get social media profiles for schema
 */
function mr9_get_social_profiles() {
    $profiles = array();
    
    $social_networks = array(
        'facebook' => get_theme_mod('mr9_facebook_url', ''),
        'twitter' => get_theme_mod('mr9_twitter_url', ''),
        'instagram' => get_theme_mod('mr9_instagram_url', ''),
        'linkedin' => get_theme_mod('mr9_linkedin_url', ''),
        'youtube' => get_theme_mod('mr9_youtube_url', '')
    );
    
    foreach ($social_networks as $network => $url) {
        if (!empty($url)) {
            $profiles[] = $url;
        }
    }
    
    return $profiles;
}

/**
 * Add Open Graph meta tags
 */
function mr9_add_open_graph_tags() {
    if (is_single() || is_page()) {
        echo '<meta property="og:type" content="article">' . "\n";
        echo '<meta property="og:title" content="' . esc_attr(get_the_title()) . '">' . "\n";
        echo '<meta property="og:description" content="' . esc_attr(mr9_custom_excerpt(160)) . '">' . "\n";
        echo '<meta property="og:url" content="' . esc_url(get_permalink()) . '">' . "\n";
        echo '<meta property="og:site_name" content="' . esc_attr(get_bloginfo('name')) . '">' . "\n";
        echo '<meta property="article:published_time" content="' . get_the_date('c') . '">' . "\n";
        echo '<meta property="article:modified_time" content="' . get_the_modified_date('c') . '">' . "\n";
        echo '<meta property="article:author" content="' . esc_attr(get_the_author()) . '">' . "\n";
        
        // Add categories and tags
        $categories = get_the_category();
        foreach ($categories as $category) {
            echo '<meta property="article:section" content="' . esc_attr($category->name) . '">' . "\n";
        }
        
        $tags = get_the_tags();
        if ($tags) {
            foreach ($tags as $tag) {
                echo '<meta property="article:tag" content="' . esc_attr($tag->name) . '">' . "\n";
            }
        }
        
        // Add featured image
        if (has_post_thumbnail()) {
            $image_url = wp_get_attachment_image_url(get_post_thumbnail_id(), 'large');
            echo '<meta property="og:image" content="' . esc_url($image_url) . '">' . "\n";
            echo '<meta property="og:image:width" content="1200">' . "\n";
            echo '<meta property="og:image:height" content="630">' . "\n";
        }
    } else {
        echo '<meta property="og:type" content="website">' . "\n";
        echo '<meta property="og:title" content="' . esc_attr(get_bloginfo('name')) . '">' . "\n";
        echo '<meta property="og:description" content="' . esc_attr(get_bloginfo('description')) . '">' . "\n";
        echo '<meta property="og:url" content="' . esc_url(home_url()) . '">' . "\n";
        echo '<meta property="og:site_name" content="' . esc_attr(get_bloginfo('name')) . '">' . "\n";
    }
}
add_action('wp_head', 'mr9_add_open_graph_tags');

/**
 * Add Twitter Card meta tags
 */
function mr9_add_twitter_cards() {
    echo '<meta name="twitter:card" content="summary_large_image">' . "\n";
    
    $twitter_handle = get_theme_mod('mr9_twitter_handle', '');
    if (!empty($twitter_handle)) {
        echo '<meta name="twitter:site" content="@' . esc_attr($twitter_handle) . '">' . "\n";
        echo '<meta name="twitter:creator" content="@' . esc_attr($twitter_handle) . '">' . "\n";
    }
    
    if (is_single() || is_page()) {
        echo '<meta name="twitter:title" content="' . esc_attr(get_the_title()) . '">' . "\n";
        echo '<meta name="twitter:description" content="' . esc_attr(mr9_custom_excerpt(160)) . '">' . "\n";
        
        if (has_post_thumbnail()) {
            $image_url = wp_get_attachment_image_url(get_post_thumbnail_id(), 'large');
            echo '<meta name="twitter:image" content="' . esc_url($image_url) . '">' . "\n";
        }
    } else {
        echo '<meta name="twitter:title" content="' . esc_attr(get_bloginfo('name')) . '">' . "\n";
        echo '<meta name="twitter:description" content="' . esc_attr(get_bloginfo('description')) . '">' . "\n";
    }
}
add_action('wp_head', 'mr9_add_twitter_cards');

/**
 * Add canonical URLs
 */
function mr9_add_canonical_url() {
    if (is_single() || is_page()) {
        echo '<link rel="canonical" href="' . esc_url(get_permalink()) . '">' . "\n";
    } elseif (is_category()) {
        echo '<link rel="canonical" href="' . esc_url(get_category_link(get_query_var('cat'))) . '">' . "\n";
    } elseif (is_tag()) {
        echo '<link rel="canonical" href="' . esc_url(get_tag_link(get_query_var('tag_id'))) . '">' . "\n";
    } elseif (is_author()) {
        echo '<link rel="canonical" href="' . esc_url(get_author_posts_url(get_query_var('author'))) . '">' . "\n";
    } elseif (is_home()) {
        echo '<link rel="canonical" href="' . esc_url(home_url()) . '">' . "\n";
    }
}
add_action('wp_head', 'mr9_add_canonical_url');

/**
 * Add meta description
 */
function mr9_add_meta_description() {
    $description = '';
    
    if (is_single() || is_page()) {
        $description = mr9_custom_excerpt(160);
    } elseif (is_category()) {
        $description = category_description();
        if (empty($description)) {
            $description = sprintf(__('Posts in %s category', 'mr9-theme'), single_cat_title('', false));
        }
    } elseif (is_tag()) {
        $description = tag_description();
        if (empty($description)) {
            $description = sprintf(__('Posts tagged with %s', 'mr9-theme'), single_tag_title('', false));
        }
    } elseif (is_author()) {
        $description = get_the_author_meta('description');
        if (empty($description)) {
            $description = sprintf(__('Posts by %s', 'mr9-theme'), get_the_author());
        }
    } elseif (is_home()) {
        $description = get_bloginfo('description');
    }
    
    if (!empty($description)) {
        echo '<meta name="description" content="' . esc_attr(wp_trim_words($description, 25, '')) . '">' . "\n";
    }
}
add_action('wp_head', 'mr9_add_meta_description');

/**
 * Add robots meta tag
 */
function mr9_add_robots_meta() {
    $robots = 'index, follow';
    
    if (is_search() || is_404()) {
        $robots = 'noindex, nofollow';
    } elseif (is_category() || is_tag() || is_author()) {
        $robots = 'index, follow, noarchive';
    } elseif (is_attachment()) {
        $robots = 'noindex, follow';
    }
    
    echo '<meta name="robots" content="' . esc_attr($robots) . '">' . "\n";
}
add_action('wp_head', 'mr9_add_robots_meta');

/**
 * Generate XML sitemap
 */
function mr9_generate_sitemap() {
    if (get_query_var('sitemap')) {
        header('Content-Type: application/xml; charset=utf-8');
        
        echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
        
        // Homepage
        echo '<url>' . "\n";
        echo '<loc>' . esc_url(home_url()) . '</loc>' . "\n";
        echo '<changefreq>daily</changefreq>' . "\n";
        echo '<priority>1.0</priority>' . "\n";
        echo '</url>' . "\n";
        
        // Posts
        $posts = get_posts(array('numberposts' => -1, 'post_status' => 'publish'));
        foreach ($posts as $post) {
            echo '<url>' . "\n";
            echo '<loc>' . esc_url(get_permalink($post->ID)) . '</loc>' . "\n";
            echo '<lastmod>' . get_the_modified_date('c', $post->ID) . '</lastmod>' . "\n";
            echo '<changefreq>weekly</changefreq>' . "\n";
            echo '<priority>0.8</priority>' . "\n";
            echo '</url>' . "\n";
        }
        
        // Pages
        $pages = get_pages();
        foreach ($pages as $page) {
            echo '<url>' . "\n";
            echo '<loc>' . esc_url(get_permalink($page->ID)) . '</loc>' . "\n";
            echo '<lastmod>' . get_the_modified_date('c', $page->ID) . '</lastmod>' . "\n";
            echo '<changefreq>monthly</changefreq>' . "\n";
            echo '<priority>0.6</priority>' . "\n";
            echo '</url>' . "\n";
        }
        
        echo '</urlset>';
        exit;
    }
}
add_action('template_redirect', 'mr9_generate_sitemap');

/**
 * Add sitemap rewrite rule
 */
function mr9_add_sitemap_rewrite() {
    add_rewrite_rule('^sitemap\.xml$', 'index.php?sitemap=1', 'top');
}
add_action('init', 'mr9_add_sitemap_rewrite');

/**
 * Add sitemap query var
 */
function mr9_add_sitemap_query_var($vars) {
    $vars[] = 'sitemap';
    return $vars;
}
add_filter('query_vars', 'mr9_add_sitemap_query_var');
