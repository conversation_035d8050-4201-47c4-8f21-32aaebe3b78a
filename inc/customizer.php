<?php
/**
 * Theme Customizer Settings
 *
 * @package MR9_Theme
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add customizer settings
 */
function mr9_customize_register($wp_customize) {
    
    // AdSense Settings Panel
    $wp_customize->add_panel('mr9_adsense_panel', array(
        'title' => __('AdSense Settings', 'mr9-theme'),
        'description' => __('Configure AdSense ads for optimal revenue generation', 'mr9-theme'),
        'priority' => 30,
    ));
    
    // General Ad Settings
    $wp_customize->add_section('mr9_general_ads', array(
        'title' => __('General Ad Settings', 'mr9-theme'),
        'panel' => 'mr9_adsense_panel',
        'priority' => 10,
    ));
    
    // Enable/Disable Ads
    $wp_customize->add_setting('mr9_enable_ads', array(
        'default' => true,
        'sanitize_callback' => 'wp_validate_boolean',
    ));
    
    $wp_customize->add_control('mr9_enable_ads', array(
        'label' => __('Enable Ads', 'mr9-theme'),
        'description' => __('Turn on/off all advertisements on the site', 'mr9-theme'),
        'section' => 'mr9_general_ads',
        'type' => 'checkbox',
    ));
    
    // AdSense Client ID
    $wp_customize->add_setting('mr9_adsense_client_id', array(
        'default' => '',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('mr9_adsense_client_id', array(
        'label' => __('AdSense Client ID', 'mr9-theme'),
        'description' => __('Enter your AdSense client ID (ca-pub-xxxxxxxxxx)', 'mr9-theme'),
        'section' => 'mr9_general_ads',
        'type' => 'text',
    ));
    
    // Auto Ads
    $wp_customize->add_setting('mr9_adsense_auto_ads', array(
        'default' => false,
        'sanitize_callback' => 'wp_validate_boolean',
    ));
    
    $wp_customize->add_control('mr9_adsense_auto_ads', array(
        'label' => __('Enable Auto Ads', 'mr9-theme'),
        'description' => __('Let Google automatically place ads on your site', 'mr9-theme'),
        'section' => 'mr9_general_ads',
        'type' => 'checkbox',
    ));
    
    // Header Ads Section
    $wp_customize->add_section('mr9_header_ads', array(
        'title' => __('Header Ads', 'mr9-theme'),
        'panel' => 'mr9_adsense_panel',
        'priority' => 20,
    ));
    
    // Enable Header Ads
    $wp_customize->add_setting('mr9_enable_header_ads', array(
        'default' => true,
        'sanitize_callback' => 'wp_validate_boolean',
    ));
    
    $wp_customize->add_control('mr9_enable_header_ads', array(
        'label' => __('Enable Header Ads', 'mr9-theme'),
        'section' => 'mr9_header_ads',
        'type' => 'checkbox',
    ));
    
    // Header Leaderboard Slot
    $wp_customize->add_setting('mr9_header_leaderboard_slot', array(
        'default' => '',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('mr9_header_leaderboard_slot', array(
        'label' => __('Header Leaderboard Ad Slot', 'mr9-theme'),
        'description' => __('728x90 leaderboard ad slot ID', 'mr9-theme'),
        'section' => 'mr9_header_ads',
        'type' => 'text',
    ));
    
    // Content Ads Section
    $wp_customize->add_section('mr9_content_ads', array(
        'title' => __('Content Ads', 'mr9-theme'),
        'panel' => 'mr9_adsense_panel',
        'priority' => 30,
    ));
    
    // Enable Content Ads
    $wp_customize->add_setting('mr9_enable_content_ads', array(
        'default' => true,
        'sanitize_callback' => 'wp_validate_boolean',
    ));
    
    $wp_customize->add_control('mr9_enable_content_ads', array(
        'label' => __('Enable Content Ads', 'mr9-theme'),
        'section' => 'mr9_content_ads',
        'type' => 'checkbox',
    ));
    
    // Above Content Ad Slot
    $wp_customize->add_setting('mr9_above_content_ad_slot', array(
        'default' => '',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('mr9_above_content_ad_slot', array(
        'label' => __('Above Content Ad Slot', 'mr9-theme'),
        'section' => 'mr9_content_ads',
        'type' => 'text',
    ));
    
    // Below Content Ad Slot
    $wp_customize->add_setting('mr9_below_content_ad_slot', array(
        'default' => '',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('mr9_below_content_ad_slot', array(
        'label' => __('Below Content Ad Slot', 'mr9-theme'),
        'section' => 'mr9_content_ads',
        'type' => 'text',
    ));
    
    // In-Article Ads
    $wp_customize->add_setting('mr9_enable_in_article_ads', array(
        'default' => true,
        'sanitize_callback' => 'wp_validate_boolean',
    ));
    
    $wp_customize->add_control('mr9_enable_in_article_ads', array(
        'label' => __('Enable In-Article Ads', 'mr9-theme'),
        'section' => 'mr9_content_ads',
        'type' => 'checkbox',
    ));
    
    // In-Article Ad Slots
    for ($i = 1; $i <= 3; $i++) {
        $wp_customize->add_setting("mr9_in_article_ad_slot_{$i}", array(
            'default' => '',
            'sanitize_callback' => 'sanitize_text_field',
        ));
        
        $wp_customize->add_control("mr9_in_article_ad_slot_{$i}", array(
            'label' => sprintf(__('In-Article Ad Slot %d', 'mr9-theme'), $i),
            'section' => 'mr9_content_ads',
            'type' => 'text',
        ));
    }
    
    // Sidebar Ads Section
    $wp_customize->add_section('mr9_sidebar_ads', array(
        'title' => __('Sidebar Ads', 'mr9-theme'),
        'panel' => 'mr9_adsense_panel',
        'priority' => 40,
    ));
    
    // Enable Sidebar Ads
    $wp_customize->add_setting('mr9_enable_sidebar_ads', array(
        'default' => true,
        'sanitize_callback' => 'wp_validate_boolean',
    ));
    
    $wp_customize->add_control('mr9_enable_sidebar_ads', array(
        'label' => __('Enable Sidebar Ads', 'mr9-theme'),
        'section' => 'mr9_sidebar_ads',
        'type' => 'checkbox',
    ));
    
    // Sidebar Ad Slots
    $sidebar_positions = array('top', 'middle', 'bottom');
    foreach ($sidebar_positions as $position) {
        $wp_customize->add_setting("mr9_sidebar_{$position}_ad_slot", array(
            'default' => '',
            'sanitize_callback' => 'sanitize_text_field',
        ));
        
        $wp_customize->add_control("mr9_sidebar_{$position}_ad_slot", array(
            'label' => sprintf(__('Sidebar %s Ad Slot', 'mr9-theme'), ucfirst($position)),
            'section' => 'mr9_sidebar_ads',
            'type' => 'text',
        ));
    }
    
    // Mobile Ads Section
    $wp_customize->add_section('mr9_mobile_ads', array(
        'title' => __('Mobile Ads', 'mr9-theme'),
        'panel' => 'mr9_adsense_panel',
        'priority' => 50,
    ));
    
    // Enable Mobile Ads
    $wp_customize->add_setting('mr9_enable_mobile_ads', array(
        'default' => true,
        'sanitize_callback' => 'wp_validate_boolean',
    ));
    
    $wp_customize->add_control('mr9_enable_mobile_ads', array(
        'label' => __('Enable Mobile Ads', 'mr9-theme'),
        'section' => 'mr9_mobile_ads',
        'type' => 'checkbox',
    ));
    
    // Mobile Sticky Ad Slot
    $wp_customize->add_setting('mr9_mobile_sticky_ad_slot', array(
        'default' => '',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('mr9_mobile_sticky_ad_slot', array(
        'label' => __('Mobile Sticky Ad Slot', 'mr9-theme'),
        'description' => __('320x50 mobile banner ad slot ID', 'mr9-theme'),
        'section' => 'mr9_mobile_ads',
        'type' => 'text',
    ));
    
    // Performance Settings Panel
    $wp_customize->add_panel('mr9_performance_panel', array(
        'title' => __('Performance Settings', 'mr9-theme'),
        'description' => __('Optimize theme performance and Core Web Vitals', 'mr9-theme'),
        'priority' => 40,
    ));
    
    // Core Web Vitals Section
    $wp_customize->add_section('mr9_core_web_vitals', array(
        'title' => __('Core Web Vitals', 'mr9-theme'),
        'panel' => 'mr9_performance_panel',
        'priority' => 10,
    ));
    
    // Monitor Core Web Vitals
    $wp_customize->add_setting('mr9_monitor_vitals', array(
        'default' => false,
        'sanitize_callback' => 'wp_validate_boolean',
    ));
    
    $wp_customize->add_control('mr9_monitor_vitals', array(
        'label' => __('Monitor Core Web Vitals', 'mr9-theme'),
        'description' => __('Track LCP, FID, and CLS metrics', 'mr9-theme'),
        'section' => 'mr9_core_web_vitals',
        'type' => 'checkbox',
    ));
    
    // SEO Settings Panel
    $wp_customize->add_panel('mr9_seo_panel', array(
        'title' => __('SEO Settings', 'mr9-theme'),
        'description' => __('Search engine optimization settings', 'mr9-theme'),
        'priority' => 50,
    ));
    
    // Social Media Section
    $wp_customize->add_section('mr9_social_media', array(
        'title' => __('Social Media', 'mr9-theme'),
        'panel' => 'mr9_seo_panel',
        'priority' => 10,
    ));
    
    // Social Media URLs
    $social_networks = array(
        'facebook' => 'Facebook',
        'twitter' => 'Twitter',
        'instagram' => 'Instagram',
        'linkedin' => 'LinkedIn',
        'youtube' => 'YouTube'
    );
    
    foreach ($social_networks as $network => $label) {
        $wp_customize->add_setting("mr9_{$network}_url", array(
            'default' => '',
            'sanitize_callback' => 'esc_url_raw',
        ));
        
        $wp_customize->add_control("mr9_{$network}_url", array(
            'label' => sprintf(__('%s URL', 'mr9-theme'), $label),
            'section' => 'mr9_social_media',
            'type' => 'url',
        ));
    }
    
    // Twitter Handle
    $wp_customize->add_setting('mr9_twitter_handle', array(
        'default' => '',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('mr9_twitter_handle', array(
        'label' => __('Twitter Handle', 'mr9-theme'),
        'description' => __('Without @ symbol', 'mr9-theme'),
        'section' => 'mr9_social_media',
        'type' => 'text',
    ));
}
add_action('customize_register', 'mr9_customize_register');
