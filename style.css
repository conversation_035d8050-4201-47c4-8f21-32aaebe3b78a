/*
Theme Name: MR9 SEO AdSense Theme
Description: High-performance WordPress theme optimized for AdSense revenue generation with 100% code-based layout
Author: MR9 Development
Version: 1.0.0
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
License: GPL v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Text Domain: mr9-theme
*/

/* ==========================================================================
   CSS Reset & Base Styles
   ========================================================================== */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    line-height: 1.6;
    scroll-behavior: smooth;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    color: #333;
    background-color: #f8f9fa;
    overflow-x: hidden;
}

/* ==========================================================================
   Typography
   ========================================================================== */

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
    color: #1a1a1a;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1.5rem;
    line-height: 1.7;
}

a {
    color: #007cba;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: #005a87;
}

/* ==========================================================================
   Layout Structure
   ========================================================================== */

.site-container {
    max-width: 1200px;
    margin: 0 auto;
    background: #fff;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

.site-header {
    background: #fff;
    border-bottom: 1px solid #e1e5e9;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.site-main {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 2rem;
    padding: 2rem;
    min-height: calc(100vh - 200px);
}

.content-area {
    min-width: 0;
}

.sidebar {
    position: sticky;
    top: 100px;
    height: fit-content;
}

.site-footer {
    background: #2c3e50;
    color: #fff;
    padding: 2rem 0;
    margin-top: 3rem;
}

/* ==========================================================================
   Header Styles
   ========================================================================== */

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.site-logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
}

.main-navigation ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.main-navigation a {
    font-weight: 500;
    color: #333;
    padding: 0.5rem 0;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.main-navigation a:hover,
.main-navigation .current-menu-item a {
    color: #007cba;
    border-bottom-color: #007cba;
}

/* ==========================================================================
   Ad Container Styles
   ========================================================================== */

.ad-container {
    margin: 2rem 0;
    text-align: center;
    position: relative;
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    padding: 1rem;
    min-height: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ad-container::before {
    content: "Publicidade";
    position: absolute;
    top: 5px;
    left: 10px;
    font-size: 0.75rem;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.ad-container[role="complementary"] {
    background: #fff;
    border-color: #ddd;
}

/* Ad Sizes */
.ad-leaderboard {
    min-height: 90px;
    max-width: 728px;
    margin: 0 auto;
}

.ad-rectangle {
    min-height: 250px;
    max-width: 300px;
    margin: 0 auto;
}

.ad-skyscraper {
    min-height: 600px;
    max-width: 160px;
}

.ad-mobile-banner {
    min-height: 50px;
    max-width: 320px;
    margin: 0 auto;
}

/* Sticky Ad Styles */
.ad-sticky {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background: #fff;
    border-top: 1px solid #ddd;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    display: none;
}

.ad-sticky.active {
    display: block;
}

/* ==========================================================================
   Post Styles
   ========================================================================== */

.post {
    background: #fff;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.post-header {
    margin-bottom: 2rem;
}

.post-title {
    font-size: 2.2rem;
    line-height: 1.2;
    margin-bottom: 1rem;
    color: #1a1a1a;
}

.post-meta {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.post-meta a {
    color: #666;
}

.post-meta a:hover {
    color: #007cba;
}

.post-content {
    font-size: 1.1rem;
    line-height: 1.8;
}

.post-content img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 1.5rem 0;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
    .site-main {
        grid-template-columns: 1fr;
        padding: 1rem;
        gap: 1rem;
    }
    
    .sidebar {
        position: static;
        order: 2;
    }
    
    .header-container {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }
    
    .main-navigation ul {
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
    }
    
    .post {
        padding: 1.5rem;
    }
    
    .post-title {
        font-size: 1.8rem;
    }
    
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }
    
    .ad-leaderboard {
        display: none;
    }
    
    .ad-mobile-banner {
        display: block;
    }
    
    .ad-sticky {
        display: block;
    }
}

@media (min-width: 769px) {
    .ad-mobile-banner {
        display: none;
    }
    
    .ad-leaderboard {
        display: block;
    }
}
